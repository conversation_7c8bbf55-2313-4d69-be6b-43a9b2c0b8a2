<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        window.faceApiAvailable = false;

        // Create and load the Face API script dynamically
        document.addEventListener('DOMContentLoaded', function() {
            var faceApiScript = document.createElement('script');
            faceApiScript.src = '<?= base_url('assets/models/face-api.min.js') ?>';

            // Add event listeners before appending to DOM
            faceApiScript.addEventListener('load', function() {
                window.faceApiAvailable = true;
                console.log('Face API library loaded successfully.');
            });

            faceApiScript.addEventListener('error', function() {
                console.warn('Face API library could not be loaded. Face detection features will be disabled.');
            });

            document.head.appendChild(faceApiScript);
        });
    </script>
    <style>
        .container {
            max-width: 100%;
            padding: 0 15px;
            margin: 0 auto;
            box-sizing: border-box;
        }

        /* Top Alert styles */
        .top-alert {
            background: #f3e7c2;
            color: #6b5b2e;
            border-radius: 12px;
            padding: 18px 18px 12px 18px;
            margin: 15px 12px;
            font-size: 16px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .top-alert i {
            color: #b89c3a;
            margin-right: 8px;
            font-size: 18px;
        }

        .top-alert .alert-title {
            font-weight: 600;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            width: 100%;
        }

        .top-alert .alert-desc {
            font-size: 15px;
            margin-bottom: 12px;
            width: 100%;
        }

        .top-alert .alert-btn {
            background: #e0d7c2;
            border: none;
            border-radius: 8px;
            padding: 12px 0;
            width: 100%;
            font-weight: 600;
            font-size: 16px;
            color: #6b5b2e;
            margin-top: 8px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .top-alert .alert-btn:hover {
            background: #d5cbb3;
        }

        /* ID Card Container styles */
        .idcard-container {
            position: relative;
            background-color: #fff;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            height: 100%;
            margin-bottom: 20px;
        }

        .idcard-title {
            margin-bottom: 15px;
            font-weight: bold;
            font-size: 18px;
            text-align: center;
            color: #444;
        }

        .idcard-bg {
            min-height: 335px;
            position: relative;
            justify-content: center;
            align-items: center;
            padding: 0;
            margin: 0;
            display: flex;
        }

        /* Ensure loading indicator is centered */
        .preview-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 30px 0;
            background-color: #f9f9f9;
            border-radius: 8px;
        }
        .element {
            position: absolute;
        }
        .element-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: visible;
        }
        .shape-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
        }
        .shape-circle {
            border-radius: 50%;
            overflow: hidden;
        }
        .photo-in-shape {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .card-size-portrait {
            aspect-ratio: 0.63; /* 54mm:86mm ratio */
            width: 100%;
        }
        .card-preview {
            width: 215px;
            height: 335px;
            position: relative;
        }

        .card-size-landscape {
            aspect-ratio: 1.59; /* 86mm:54mm ratio */
            width: 100%;
        }
        .action-btns {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }
        .action-btns .approve-btn {
            background: #3DA755;
            color: #fff;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 17px;
            padding: 8px 30px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            box-shadow: 0 2px 5px rgba(61, 167, 85, 0.2);
            min-width: 200px;
        }
        .action-btns .approve-btn:hover:not(:disabled) {
            background: #34964a;
        }
        .action-btns .approve-btn:disabled {
            background: #d3d3d3;
            color: #888;
            cursor: not-allowed;
            box-shadow: none;
        }
        .action-btns .update-btn {
            background: #6C40FB;
            color: #fff;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 17px;
            padding: 8px 30px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            box-shadow: 0 2px 5px rgba(108, 64, 251, 0.2);
        }
        .action-btns .update-btn:hover:not(:disabled) {
            background: #5c35e0;
        }
        .action-btns .update-btn:disabled {
            background: #d3d3d3;
            color: #888;
            cursor: not-allowed;
            box-shadow: none;
        }

        /* Status badge */
        .status-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin: 10px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .status-in-review {
            background-color: #E6F4FF;
            color: #0088E8;
        }
        .status-approved {
            background-color: #ECF8EF;
            color: #3DA755;
        }
        .status-modify {
            background-color: #FFF7E6;
            color: #E89B00;
        }
        .status-removed {
            background-color: #FDECEC;
            color: #D93E39;
        }
        /* Custom approval loading overlay */
        #customApprovalLoadingOverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
        }
        .custom-approval-loading-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(3px);
        }
        .custom-approval-loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            width: 85%;
            max-width: 320px;
            text-align: center;
        }
        .custom-approval-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #333;
        }
        .custom-approval-desc {
            font-size: 15px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        .custom-approval-progress-img {
            margin: 20px auto;
            position: relative;
            width: 120px;
            height: 120px;
        }
        .circular-progress {
            position: relative;
            width: 120px;
            height: 120px;
        }
        .circular-progress svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }
        .circular-progress circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
        }
        .progress-bg {
            stroke: #f0f0f0;
        }
        .progress-bar {
            stroke: #3DA755;
            stroke-dasharray: 283;
            stroke-dashoffset: 283;
            transition: stroke-dashoffset 0.5s ease;
        }
        .custom-approval-progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            font-weight: 700;
            color: #3DA755;
        }

        /* Desktop specific styles */
        .card-container {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .card-wrapper {
            flex: 0 0 auto;
        }

        .status-container {
            text-align: center;
            margin-bottom: 20px;
        }

        /* Carousel specific styles */
        #idCardsCarousel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            /* margin: 20px 0; */
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        #idCardsCarousel .carousel-item {
            transition: transform 0.6s ease-in-out;
        }

        #idCardsCarousel .carousel-control-prev,
        #idCardsCarousel .carousel-control-next {
            width: 5%;
            color: #007bff;
        }

        #idCardsCarousel .carousel-control-prev-icon,
        #idCardsCarousel .carousel-control-next-icon {
            background-color: #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
        }

        #idCardsCarousel .carousel-control-prev:hover .carousel-control-prev-icon,
        #idCardsCarousel .carousel-control-next:hover .carousel-control-next-icon {
            background-color: #0056b3;
        }

        /* Download button styles */
        .btn-primary.btn-sm {
            background: #007bff;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        }

        .btn-primary.btn-sm:hover {
            background: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }

        .btn-primary.btn-sm i {
            margin-right: 6px;
        }

        /* Single card centering */
        .card-container:has(.card-wrapper:only-child) {
            justify-content: center;
        }

        /* Fallback for browsers that don't support :has() */
        .card-container .card-wrapper:only-child {
            margin: 0 auto;
        }
    </style>

    <ul class="breadcrumb">
        <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
        <li>ID Card</li>
    </ul>

    <div class="col-md-12">
        <div class="panel panel-default new-panel-style_3">
            <div class="card-header panel_heading_new_style_staff_border">
                <div class="row" style="margin: 0px;">
                    <div class="col-md-9 pl-0">
                        <h3 class="card-title panel_title_new_style_staff" style="margin-top: 3px;">
                           ID Card
                        </h3>
                    </div>
                </div>
            </div>
            <div class="panel-body">
                
                <!-- Unified ID Cards Carousel Section -->
                <div>
                    <?php
                    // Check if student has ID card data
                    $hasStudentIdCard = !empty($student_details) && !empty($student_details[0]);

                    // Count valid parent cards
                    $validParentCards = 0;
                    $parentTypes = ['Father', 'Mother', 'Guardian', 'Guardian_2', 'Driver', 'Driver_2'];
                    if (!empty($parent_idcards)) {
                        foreach ($parentTypes as $parentType) {
                            foreach ($parent_idcards as $card) {
                                if ($card->avatar_type == $parentType && (!empty($card->front_page_img_url) || !empty($card->back_page_img_url))) {
                                    $validParentCards++;
                                    break;
                                }
                            }
                        }
                    }

                    $totalSlides = ($hasStudentIdCard ? 1 : 0) + $validParentCards;

                    // If no ID cards at all, show message
                    if ($totalSlides == 0): ?>
                        <div class="no-data-display text-center" style="margin: 40px auto; font-size: 18px; color: #888;">
                            ID card not found
                        </div>
                    <?php else: ?>
                        <div id="idCardsCarousel" class="carousel slide" data-ride="carousel">
                            <div class="carousel-inner">
                                <?php
                                $isFirstSlide = true;

                                // Student ID Card - Show only if student has ID card data
                                if ($hasStudentIdCard): ?>
                                <div class="carousel-item <?= $isFirstSlide ? 'active' : '' ?>">
                                    <?php $isFirstSlide = false; ?>
                                    <h4 class="text-center mb-3" style="color: #444; font-weight: 600;">Student ID Card</h4>

                                    <!-- ID Card Status - Only for Student -->
                                    <div class="status-container">
                                        <div id="entityStatusBadge" class="status-badge status-in-review" style="display:none;"></div>
                                    </div>

                                    <div class="card-container">
                                        <div class="card-wrapper">
                                            <div class="idcard-container">
                                                <div class="idcard-title">Student Front</div>
                                                <div class="idcard-bg">
                                                    <div id="frontCardPreview" class="card-preview">
                                                        <!-- Front preview will be rendered here -->
                                                        <div class="preview-loading">
                                                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                                                            <p>Loading preview...</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-wrapper">
                                            <div class="idcard-container">
                                                <div class="idcard-title">Student Back</div>
                                                <div class="idcard-bg">
                                                    <div id="backCardPreview" class="card-preview">
                                                        <div class="preview-loading">
                                                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                                                            <p>Loading preview...</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Action Buttons - Only for Student ID Card -->
                                    <div class="action-btns">
                                        <button id="approveBtn" class="approve-btn" disabled>
                                            <i class="fa fa-spinner fa-spin"></i> LOADING...
                                        </button>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Parent ID Cards - Show if parent cards exist -->
                                <?php if ($validParentCards > 0):
                                foreach ($parentTypes as $index => $parentType):
                                    $parentCard = null;
                                    foreach ($parent_idcards as $card) {
                                        if ($card->avatar_type == $parentType) {
                                            $parentCard = $card;
                                            break;
                                        }
                                    }
                                    // Only show the slide if the parent card exists and has at least one image
                                    if ($parentCard && (!empty($parentCard->front_page_img_url) || !empty($parentCard->back_page_img_url))):
                                ?>
                                <div class="carousel-item <?= $isFirstSlide ? 'active' : '' ?>">
                                    <?php $isFirstSlide = false; ?>
                                    <h4 class="text-center mb-3" style="color: #444; font-weight: 600;"><?= ucfirst(str_replace('_', ' ', $parentType)) ?> ID Card</h4>
                                    <div class="card-container">
                                        <?php if (!empty($parentCard->front_page_img_url)): ?>
                                        <div class="card-wrapper">
                                            <div class="idcard-container">
                                                <div class="idcard-title"><?= ucfirst(str_replace('_', ' ', $parentType)) ?> - Front</div>
                                                <div class="idcard-bg">
                                                    <div class="card-preview">
                                                        <img src="<?= $parentCard->front_page_img_url ?>" alt="<?= $parentType ?> Front ID Card" class="img-fluid" style="max-width: 100%; height: auto;">
                                                    </div>
                                                </div>
                                                <div class="text-center mt-4">
                                                    <a data-file-path="<?= $parentCard->front_page_img_url ?>"
                                                        data-file-name="<?= $parentType ?>_IDCard.jpg"
                                                        onclick="downloadIdcard(this); return false;" href="#" class="btn btn-primary btn-sm">
                                                        <i class="fa fa-download"></i> Download Front
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <?php if (!empty($parentCard->back_page_img_url)): ?>
                                        <div class="card-wrapper">
                                            <div class="idcard-container">
                                                <div class="idcard-title"><?= ucfirst(str_replace('_', ' ', $parentType)) ?> - Back</div>
                                                <div class="idcard-bg">
                                                    <div class="card-preview">
                                                        <img src="<?= $parentCard->back_page_img_url ?>" alt="<?= $parentType ?> Back ID Card" class="img-fluid" style="max-width: 100%; height: auto;">
                                                    </div>
                                                </div>
                                                <div class="text-center mt-4">
                                                    <a data-file-path="<?= $parentCard->back_page_img_url ?>"
                                                        data-file-name="<?= $parentType ?>_IDCard.jpg"
                                                        onclick="downloadIdcard(this); return false;" href="#" class="btn btn-primary btn-sm">
                                                        <i class="fa fa-download"></i> Download Back
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; endforeach; endif; ?>
                            </div>

                            <!-- Carousel Controls - Show only if more than 1 slide -->
                            <?php if ($totalSlides > 1): ?>
                            <a class="carousel-control-prev" href="#idCardsCarousel" role="button" data-slide="prev">
                                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                <span class="sr-only">Previous</span>
                            </a>
                            <a class="carousel-control-next" href="#idCardsCarousel" role="button" data-slide="next">
                                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                <span class="sr-only">Next</span>
                            </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Custom Approval Loading Overlay -->
                <div id="customApprovalLoadingOverlay">
                    <div class="custom-approval-loading-bg"></div>
                    <div class="custom-approval-loading-content">
                        <div class="custom-approval-title">Processing your ID card</div>
                        <div class="custom-approval-desc">We're working on your ID card! Just hang tight for a bit...</div>
                        <div class="custom-approval-progress-img">
                            <!-- Circular progress bar -->
                            <div id="customApprovalProgressCircle" class="circular-progress">
                                <svg viewBox="0 0 100 100" width="120" height="120">
                                    <circle class="progress-bg" cx="50" cy="50" r="45"/>
                                    <circle class="progress-bar" cx="50" cy="50" r="45"/>
                                </svg>
                                <div class="custom-approval-progress-text" id="customApprovalProgressText">0%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Fix for jQuery plugins that might not be available
        $.fn.tooltip = $.fn.tooltip || function() { return this; };
        $.mpb = $.mpb || function() { return this; };

        // Global variables
        let order_id = '<?php echo isset($tempalte_details->order_id) ? $tempalte_details->order_id : ""; ?>';
        let template_id = '<?php echo isset($tempalte_details->idcard_template_id) ? $tempalte_details->idcard_template_id : ""; ?>';
        let student_id = '<?php
            if (isset($student_details[0]->sa_id)) {
                echo $student_details[0]->sa_id;
            } else if (isset($student_details[0]->id)) {
                echo $student_details[0]->id;
            } else {
                echo "";
            }
        ?>';
        let id_card_for = 'Student';
        let current_percentage = 0;
        let in_progress_promises = 0;

        // Safely encode entity data with error handling
        let entityData;
        try {
            entityData = <?php echo isset($student_details[0]) ? json_encode($student_details[0]) : 'null'; ?>;
            console.log('Entity data loaded:', entityData);
        } catch (e) {
            console.error('Error loading entity data:', e);
            entityData = null;
        }

        // Check if order_id is from idcard_template_order_id field
        if (!order_id && entityData && entityData.idcard_template_order_id) {
            order_id = entityData.idcard_template_order_id;
            console.log('Using order_id from entity data:', order_id);
        }

        // Make sure student_id is set - check both id and sa_id fields
        if (!student_id) {
            if (entityData && entityData.sa_id) {
                student_id = entityData.sa_id;
                console.log('Using student_id from entity data (sa_id):', student_id);
            } else if (entityData && entityData.id) {
                student_id = entityData.id;
                console.log('Using student_id from entity data (id):', student_id);
            }
        }

        // Make sure template_id is set
        if (!template_id && entityData && entityData.idcard_template_id) {
            template_id = entityData.idcard_template_id;
            console.log('Using template_id from entity data:', template_id);
        }
        // Field mappings for ID card
        const FIELD_MAPPINGS = [
            { field: '[[NAME]]', key: 'name' },
                { field: '[[ID]]', key: 'employee_code' },
                { field: '[[DEPARTMENT]]', key: 'department' },
                { field: '[[DESIGNATION]]', key: 'designation' },
                { field: '[[CONTACT]]', key: 'contact' },
                { field: '[[PARENT_NAME]]', key: 'parent_name' },
                { field: '[[FATHER_NAME]]', key: 'father_name' },
                { field: '[[MOTHER_NAME]]', key: 'mother_name' },
                { field: '[[FATHER_CONTACT]]', key: 'father_contact' },
                { field: '[[MOTHER_CONTACT]]', key: 'mother_contact' },
                { field: '[[BLOOD_GROUP]]', key: 'blood_group' },
                { field: '[[DATE_OF_BIRTH]]', key: 'date_of_birth' },
                { field: '[[DOB]]', key: 'dob' },
                { field: '[[QR_CODE]]', key: 'qr_code' },
                { field: '[[BAR_CODE]]', key: 'bar_code' },
                { field: '[[SIGNATURE]]', key: 'signature' },
                { field: '[[LOGO]]', key: 'logo' },
                { field: '[[ADDRESS]]', key: 'address' },
                { field: '[[EMAIL]]', key: 'email' },
                { field: '[[PHONE]]', key: 'phone' },
                { field: '[[WEBSITE]]', key: 'website' },
                { field: '[[SOCIAL_MEDIA]]', key: 'social_media' },
                { field: '[[QR_CODE_URL]]', key: 'qr_code_url' },
                { field: '[[BAR_CODE_URL]]', key: 'bar_code_url' },
                { field: '[[SIGNATURE_URL]]', key: 'signature_url' },
                { field: '[[LOGO_URL]]', key: 'logo_url' },
                { field: '[[ADDRESS_URL]]', key: 'address_url' },
                { field: '[[EMAIL_URL]]', key: 'email_url' },
                { field: '[[PHONE_URL]]', key: 'phone_url' },
                { field: '[[WEBSITE_URL]]', key: 'website_url' },
                { field: '[[SOCIAL_MEDIA_URL]]', key: 'social_media_url' },
                { field: '[[PHOTO]]', key: 'photo' },
                { field: '[[GRADE_SECTION]]', key: 'grade_section' },
                { field: '[[STUDENT_ADDRESS]]', key: 'address' },
                { field: '[[FATHER_ADDRESS]]', key: 'father_address' },
                { field: '[[MOTHER_ADDRESS]]', key: 'mother_address' },
                { field: '[[FATHER_PHOTO]]', key: 'father_photo' },
                { field: '[[MOTHER_PHOTO]]', key: 'mother_photo' },
                { field: '[[GRADE]]', key: 'grade' },
                { field: '[[COMBINATION]]', key: 'combination' },
                { field: '[[ENROLLMENT_NO]]', key: 'enrollment_no' },
                { field: '[[ALPHA_ROLL_NO]]', key: 'alpha_rollnum' },
                { field: '[[ADMISSION_NO]]', key: 'admission_no' },
                { field: '[[RELATION_TYPE]]', key: 'relation_type' },
                { field: '[[SIBLING_NAME]]', key: 'sibling_name' },
                { field: '[[NAME_CLASS]]', key: 'name_class' },
                { field: '[[SPOUSE_NAME]]', key: 'spouse_name' },
                { field: '[[STAFF_TYPE]]', key: 'staff_type' },
        ];

        // Modal logic removed

        // Make sure all elements exist before trying to access them
        function safeAddEventListener(id, event, callback) {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener(event, callback);
            }
        }

        // Safely handle any references to removed elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add safe event listeners for elements that might not exist
            safeAddEventListener('modalConfirmBtn', 'click', function() {
                // This is a fallback for any code that might try to use the removed modal
                console.log('Modal confirm button clicked, but modal has been removed');
            });

            safeAddEventListener('openModalBtn', 'click', function() {
                // This is a fallback for any code that might try to use the removed modal
                console.log('Open modal button clicked, but modal has been removed');
            });

            safeAddEventListener('modalLaterBtn', 'click', function(e) {
                // This is a fallback for any code that might try to use the removed modal
                if (e) e.preventDefault();
                console.log('Modal later button clicked, but modal has been removed');
            });
        });


        // Face detection functions
        function applyFaceDetection(photoId, photoUrl) {
            if (!photoUrl) {
                return;
            }

            const img = document.getElementById(photoId);
            if (!img) {
                return;
            }

            // Set default positioning
            $(img).css({
                'object-position': 'center top'
            });

            // Skip face detection for S3 URLs or invalid URLs
            if (photoUrl.includes('wasabisys.com') || !isValidUrl(photoUrl)) {
                return;
            }

            // Set cross-origin attribute for external images
            img.crossOrigin = "anonymous";

            // Apply basic positioning based on image dimensions
            if (!img.complete) {
                img.onload = function() {
                    applyBasicPositioning(img);
                    incrementShapeElementsLoaded(); // Make sure to increment counter
                };
                img.onerror = function() {
                    console.log('Image load error, using default positioning');
                    incrementShapeElementsLoaded(); // Make sure to increment counter on error too
                };
            } else {
                applyBasicPositioning(img);
                incrementShapeElementsLoaded(); // Make sure to increment counter
            }
        }

        // Apply basic positioning based on image dimensions
        function applyBasicPositioning(img) {
            try {
                if (!img.naturalWidth || !img.naturalHeight) {
                    return;
                }

                // For portrait images, position slightly higher
                if (img.naturalHeight > img.naturalWidth) {
                    $(img).css({
                        'object-position': 'center 25%'
                    });
                } else {
                    // For landscape images, position slightly lower
                    $(img).css({
                        'object-position': 'center 35%'
                    });
                }
            } catch (error) {
                console.log('Error in applyBasicPositioning:', error);
            }
        }

        function isValidUrl(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }

        // Load Face API models
        const loadFaceApiModels = async () => {
            // Check if Face API is available
            if (!window.faceApiAvailable || typeof faceapi === 'undefined') {
                console.warn('Face API is not available. Face detection features will be disabled.');
                return false; // Return false to indicate Face API is not available
            }

            try {
                await Promise.all([
                    faceapi.nets.tinyFaceDetector.loadFromUri('<?= base_url("assets/models/models") ?>'),
                    faceapi.nets.faceLandmark68Net.loadFromUri('<?= base_url("assets/models/models") ?>'),
                    faceapi.nets.faceRecognitionNet.loadFromUri('<?= base_url("assets/models/models") ?>'),
                    faceapi.nets.faceExpressionNet.loadFromUri('<?= base_url("assets/models/models") ?>'),
                ]);
                console.log('Face API Models Loaded');
                return true; // Return true to indicate Face API models are loaded
            } catch (error) {
                console.error('Error loading Face API models:', error);
                return false; // Return false to indicate Face API models failed to load
            }
        };

        // Track loading state of shape elements
        window.shapeElementsLoading = 0;
        window.shapeElementsLoaded = 0;

        // Function to increment loaded shape elements counter
        function incrementShapeElementsLoaded() {
            window.shapeElementsLoaded++;

            // If all shape elements are loaded, update the action buttons
            if (window.shapeElementsLoaded >= window.shapeElementsLoading) {
                // Reset counters for next load
                window.shapeElementsLoading = 0;
                window.shapeElementsLoaded = 0;

                // Update button state based on entity status
                if (entityData && entityData.status === 'approved') {
                    $('#approveBtn').prop('disabled', true).html('APPROVED <i class="fa fa-check"></i>');
                } else {
                    // The button state will be controlled by the profile confirmation check in document.ready
                    $('#approveBtn').html('<i class="fa fa-check"></i> APPROVE');
                }
            }
        }



        // Function to get field value from entity data
        function getFieldValue(fieldName, entityData) {
            const mapping = FIELD_MAPPINGS.find(m => m.field === fieldName);
            return mapping ? entityData[mapping.key] || '' : '';
        }

        // Function to hide student ID card section when no data is available
        function hideStudentIdCardSection() {
            // Check if student ID card slide exists
            const studentSlide = $('.carousel-item').first();
            const studentTitle = studentSlide.find('h4').text();

            if (studentTitle.includes('Student ID Card')) {
                // Hide the student ID card slide
                studentSlide.hide();

                // If there are parent cards, make the first parent card active
                const nextSlide = $('.carousel-item').not(':first').first();
                if (nextSlide.length > 0) {
                    studentSlide.removeClass('active');
                    nextSlide.addClass('active');
                } else {
                    // If no parent cards either, hide the entire carousel and show message
                    $('#idCardsCarousel').hide();
                    if ($('.no-data-display').length === 0) {
                        $('#idCardsCarousel').after('<div class="no-data-display text-center" style="margin: 40px auto; font-size: 18px; color: #888;">ID card not found</div>');
                    }
                }
            }
        }

        // Function to render ID card preview
        function renderPreview(entityData) {
            console.log('Rendering preview with entity data:', entityData);

            // Check if order_id is available
            if (!order_id) {
                console.log('Order ID is missing. Student ID card not available.');
                hideStudentIdCardSection();
                return;
            }

            // Show loading indicator in the preview areas
            $('#frontCardPreview').html('<div class="preview-loading"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading preview...</p></div>');
            $('#backCardPreview').html('<div class="preview-loading"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading preview...</p></div>');

            // Reset shape element counters
            window.shapeElementsLoading = 0;
            window.shapeElementsLoaded = 0;

            // Disable approve button during loading
            $('#approveBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> LOADING...');

            // Update status based on entity data
            if (entityData && entityData.status) {
                
                if (entityData.status === 'approved') {
                    $('#entityStatusBadge')
                        .text('Approved')
                        .attr('class', 'status-badge status-approved') .show();

                    // Disable approve button and update text when status is already approved
                    $('#approveBtn').prop('disabled', true).html('APPROVED <i class="fa fa-check"></i>');
                } else if (entityData.status === 'modify') {
                    $('#entityStatusBadge')
                        .text('Modify')
                        .attr('class', 'status-badge status-modify') .show();
                } else if (entityData.status === 'removed') {
                    $('#entityStatusBadge')
                        .text('Removed')
                        .attr('class', 'status-badge status-removed') .show();
                } else if (entityData.status === 'in review') {
                    $('#entityStatusBadge')
                        .text('In Review')
                        .attr('class', 'status-badge status-in-review') .show();
                } else {
                    $('#entityStatusBadge').hide();
                }
            }
            console.log('Fetching template with order_id:', order_id);
            $.ajax({
                url: '<?php echo site_url("parent_controller/getIdCardTemplate"); ?>',
                type: 'post',
                data: { order_id: order_id },
                success: function(response) {
                    console.log('Template response received:', response);
                    try {
                        const templateData = JSON.parse(response);
                        console.log('Parsed template data:', templateData);

                        if (templateData && templateData.template) {
                            const template = templateData.template;
                            console.log('Template found:', template);

                            let frontDesign, backDesign;
                            try {
                                frontDesign = JSON.parse(template.front_design);
                                backDesign = JSON.parse(template.back_design);
                                console.log('Parsed designs - Front:', frontDesign, 'Back:', backDesign);
                            } catch (parseError) {
                                console.error('Error parsing design JSON:', parseError);
                                renderSimplePreview(entityData);
                                return;
                            }

                            $('#frontCardPreview').empty();
                            renderDesign('#frontCardPreview', frontDesign, entityData);

                            $('#backCardPreview').empty();
                            renderDesign('#backCardPreview', backDesign, entityData);

                            // If no shape elements were found, update button state based on entity status
                            if (window.shapeElementsLoading === 0) {
                                if (entityData && entityData.status === 'approved') {
                                    $('#approveBtn').prop('disabled', true).html('APPROVED <i class="fa fa-check"></i>');
                                }
                            }
                        } else {
                            console.warn('No template found in response, hiding student ID card section');
                            hideStudentIdCardSection();
                        }
                    } catch (e) {
                        console.error('Error parsing template data:', e);
                        hideStudentIdCardSection();
                    }
                },
                error: function(err) {
                    console.error('Error fetching template data:', err);
                    hideStudentIdCardSection();
                }
            });
        }

        // Function to render design
        function renderDesign(container, design, entityData) {
            const $container = $(container);
            $container.addClass(`card-size-${design.styles?.size || 'portrait'}`);

            if (design.styles && design.styles.backgroundColor) {
                $container.css('background-color', design.styles.backgroundColor);
            }

            // Count shape elements that need to be loaded
            if (design.elements) {
                design.elements.forEach(element => {
                    if (element.type === 'shape') {
                        window.shapeElementsLoading++;
                    }
                });
            }

            design.elements?.forEach(element => {
                // For address fields, use relative positioning and auto height
                let isAddressField =
                    element.type === 'field' &&
                    (
                        element.properties.fieldName === '[[ADDRESS]]' ||
                        element.properties.fieldName === '[[STUDENT_ADDRESS]]' ||
                        element.properties.fieldName === '[[FATHER_ADDRESS]]' ||
                        element.properties.fieldName === '[[MOTHER_ADDRESS]]'
                    );

                const $element = $('<div>').addClass('element').css({
                    position: isAddressField ? 'relative' : 'absolute',
                    left: element.x + 'px',
                    top: element.y + 'px',
                    width: element.width + 'px',
                    height: isAddressField ? 'auto' : (element.height + 'px'),
                    zIndex: element.zIndex || 0
                });

                switch (element.type) {
                    case 'text':
                        $element.html(`<div class="element-content">${element.properties.text || ''}</div>`);
                        $element.find('.element-content').css({
                            'font-family': element.properties.font,
                            'font-size': `${element.properties.size}px`,
                            'color': element.properties.color,
                            'font-weight': element.properties.bold ? 'bold' : 'normal',
                            'font-style': element.properties.italic ? 'italic' : 'normal',
                            'text-decoration': element.properties.underline ? 'underline' : 'none',
                            'text-align': element.properties.textAlign || 'left',
                            'background-color': element.properties.backgroundColor || 'transparent',
                            'border': element.properties.strokeWidth ?
                                `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                            'padding': '2px 5px'
                        });
                        break;

                    case 'field':
                        const value = getFieldValue(element.properties.fieldName, entityData);
                        $element.html(`<div class="element-content">${value}</div>`);

                        const fixedHeight = element.height || 48;

                        if (
                            element.properties.fieldName === '[[ADDRESS]]' ||
                            element.properties.fieldName === '[[STUDENT_ADDRESS]]' ||
                            element.properties.fieldName === '[[FATHER_ADDRESS]]' ||
                            element.properties.fieldName === '[[MOTHER_ADDRESS]]'
                        ) {
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${element.properties.size}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? 'bold' : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': element.properties.textAlign || 'left',
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '12px',
                                'width': '100%',
                                'min-height': fixedHeight + 'px',
                                'height': 'auto',
                                'max-height': 'none',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'pre-line',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block'
                            });
                        } else if (
                            element.properties.fieldName === '[[NAME]]'
                        ) {
                            let fontSize = element.properties.size || 18;
                            let nameValue = value || '';
                            let textAlign = element.properties.textAlign || 'left';

                            let elementWidth = element.width || 215; // 

                            $element.addClass('name-field');
                            const charWidthFactor = 0.58;
                           const containerWidthInChars = Math.floor(elementWidth / (fontSize * charWidthFactor));

                           if (nameValue.length > containerWidthInChars * 0.8) {
                                // Text is approaching container width limit
                                const ratio = containerWidthInChars / nameValue.length;
                               // More granular font size adjustments
                                if (ratio < 0.3) {
                                    // Extremely long text
                                    fontSize = Math.max(6, fontSize * 0.45);
                                } else if (ratio < 0.4) {
                                    // Very long text
                                    fontSize = Math.max(7, fontSize * 0.5);

                                } else if (ratio < 0.5) {
                                    // Long text
                                    fontSize = Math.max(8, fontSize * 0.6);

                                } else if (ratio < 0.6) {
                                    // Moderately long text
                                    fontSize = Math.max(9, fontSize * 0.5);
                                } else if (ratio < 0.7) {
                                    // Slightly long text
                                    fontSize = Math.max(9, fontSize * 0.5);
                                } else if (ratio < 0.8) {
                                    // Just a bit too long
                                    fontSize = Math.max(9, fontSize * 0.6);
                                }else if(ratio < 0.9){
                                    fontSize = Math.max(8, fontSize * 0.7);
                                }else if(ratio < 0.95){
                                    fontSize = Math.max(9, fontSize * 0.8);
                                }else if(ratio < 1.2){
                                    fontSize = Math.max(9, fontSize * 0.9);
                                }else if(ratio < 1.5) {
                                    fontSize = Math.max(10, fontSize * 0.9);
                                }
                            }
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${fontSize}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? 'bold' : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': textAlign,
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px',
                                'width': '100%',
                                'height':   'auto',
                                'max-height': 'none',
                                'box-sizing': 'border-box',
                                'overflow': 'visible',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': element.properties.textTransform || 'capitalize',
                            });
                        } else if (
                            element.properties.fieldName === '[[PARENT_NAME]]'
                        ) {
                            let fontSize = element.properties.size || 16;
                            let parentNameValue = value || '';
                            let textAlign = element.properties.textAlign || 'left';
                            let elementWidth = element.width || 215;
                            $element.addClass('parent-name-field');

                            const charWidthFactor = 0.58;
                            const containerWidthInChars = Math.floor(elementWidth / (fontSize * charWidthFactor));

                            // Adjust font size based on both text length and available width
                            if (parentNameValue.length > containerWidthInChars * 0.8) {
                                // Text is approaching container width limit
                                const ratio = containerWidthInChars / parentNameValue.length;
                               // More granular font size adjustments
                            
                                if (ratio < 0.3) {
                                    // Extremely long text
                                    fontSize = Math.max(6, fontSize * 0.45);
                                } else if (ratio < 0.4) {
                                    // Very long text
                                    fontSize = Math.max(7, fontSize * 0.5);

                                } else if (ratio < 0.5) {
                                    // Long text
                                    fontSize = Math.max(8, fontSize * 0.6);

                                } else if (ratio < 0.6) {
                                    // Moderately long text
                                    fontSize = Math.max(9, fontSize * 0.5);
                                } else if (ratio < 0.7) {
                                    // Slightly long text
                                    fontSize = Math.max(9, fontSize * 0.5);
                                } else if (ratio < 0.8) {
                                    // Just a bit too long
                                    fontSize = Math.max(9, fontSize * 0.6);
                                }else if(ratio < 0.9){
                                    fontSize = Math.max(10, fontSize * 0.7);
                                }else if(ratio < 0.95){
                                    fontSize = Math.max(11, fontSize * 0.8);
                                }else if(ratio < 1.2){
                                    fontSize = Math.max(10, fontSize * 0.9);
                                }else if(ratio < 1.5) {
                                    fontSize = Math.max(9, fontSize * 0.9);
                                }
                            }
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${fontSize}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? 'bold' : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': textAlign,
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px',
                                'width': '100%',
                                'height': fixedHeight + 'px',
                                'max-height': fixedHeight + 'px',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block'
                            });
                        }else if (element.properties.fieldName === '[[QR_CODE]]') {
                            if (entityData.qr_code) {
                                // Display QR code number below the QR code image
                                $element.find('.element-content').css({
                                    'flex-direction': 'column',
                                    'align-items': 'center',
                                    'justify-content': 'center'
                                });

                                // Use a QR code image if available, else generate using API
                                let qrCodeImg = '';
                                if (entityData.qr_code_url) {
                                    qrCodeImg = `<img src="${entityData.qr_code_url}" alt="QR Code" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(qrCodeImg);
                                } else {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');
                                    generate_qr_code(entityData.qr_code, element.properties.qr_size, function(generatedQRCode) {
                                        qrCodeImg = `<img src="${generatedQRCode}" alt="QR Code">`;
                                        $element.find('.element-content').html(qrCodeImg);
                                    });
                                }
                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;">No QR Code</span>');
                            }
                        } else {
                            // Apply base styling for all other fields
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${element.properties.size}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? 'bold' : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': element.properties.textAlign || 'left',
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px',
                                'width': '100%',
                                'height': fixedHeight + 'px',
                                'max-height': fixedHeight + 'px',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block'
                            });
                        }
                        break;

                    case 'shape':
                        $element.html('<div class="element-content shape-container"><i class="fa fa-spinner fa-spin fa-2x"></i></div>');
                        const $shapeContent = $element.find('.element-content');
                        $shapeContent.addClass(`shape-${element.properties.shapeType}`);

                        let photoUrl = entityData.picture_url || element.properties.defaultPhoto || '<?= base_url("assets/img/icons/profile.png") ?>';
                        if (photoUrl.includes('wasabisys.com')) {
                            $.ajax({
                                url: '<?php echo site_url("parent_controller/getImageAsBase64"); ?>',
                                type: 'POST',
                                data: {
                                    image_url: photoUrl
                                },
                                dataType: 'json',
                                success: function(response) {
                                    if (response.success && response.base64) {
                                        const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                        $shapeContent.html(`<img id="${imgId}" src="${response.base64}" class="photo-in-shape">`);
                                        applyFaceDetection(imgId, response.base64);
                                        incrementShapeElementsLoaded();
                                    } else {
                                        console.warn('Base64 conversion failed, using original URL');
                                        const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                        $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                                        applyFaceDetection(imgId, photoUrl);
                                        incrementShapeElementsLoaded();
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('Error fetching base64 image:', error);
                                    const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                    $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                                    applyFaceDetection(imgId, photoUrl);
                                    incrementShapeElementsLoaded();
                                }
                            });
                        } else {
                            const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                            $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                            applyFaceDetection(imgId, photoUrl);
                            incrementShapeElementsLoaded();
                        }
                        $shapeContent.css({
                            'background-color': element.properties.fillColor || 'transparent',
                            'border': `${element.properties.borderWidth || 0}px solid ${element.properties.borderColor || '#000000'}`,
                            'overflow': 'hidden'
                        });
                        break;

                    case 'image':
                        if (element.properties.fieldName === '[[PHOTO]]') {
                            const photoUrl = entityData.photo || element.properties.src || '<?= base_url("assets/images/default-profile.jpg") ?>';
                            const imageClass = element.properties.shapeType === 'circle' ?
                                'element-content photo-in-circle' : 'element-content';
                            const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                            $element.html(`<img id="${imgId}" src="${photoUrl}" class="${imageClass}">`);
                            applyFaceDetection(imgId, photoUrl);
                        } else {
                            $element.html(`<img src="${element.properties.src || ''}" class="element-content">`);
                        }
                        break;
                }

                $container.append($element);
            });
        }

        // Function to render simple preview
        function renderSimplePreview(entityData) {
            console.log('Rendering simple preview with entity data:', entityData);

            // Get student name and ID safely
            const studentName = entityData && entityData.name ? entityData.name : 'Student Name';
            const studentId = entityData && entityData.admission_no ? entityData.admission_no : 'ID Number';

            const frontHtml = `
                <div style="width: 100%; height: 300px; position: relative; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 8px;">
                    <!-- X marks -->
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
                        <line x1="0" y1="0" x2="100%" y2="100%" stroke="#ccc" stroke-width="2"/>
                        <line x1="100%" y1="0" x2="0" y2="100%" stroke="#ccc" stroke-width="2"/>
                    </svg>

                    <!-- Content overlay -->
                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 2;">
                        <div style="background-color: rgba(255,255,255,0.9); padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); width: 80%;">
                            <div style="font-size: 16px; color: #333; font-weight: bold; margin-bottom: 10px;">
                                ID CARD PREVIEW
                            </div>
                            <div style="color: #666;">
                                <strong>${studentName}</strong><br>
                                ${studentId}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const backHtml = `
                <div style="width: 100%; height: 300px; position: relative; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 8px;">
                    <!-- X marks -->
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
                        <line x1="0" y1="0" x2="100%" y2="100%" stroke="#ccc" stroke-width="2"/>
                        <line x1="100%" y1="0" x2="0" y2="100%" stroke="#ccc" stroke-width="2"/>
                    </svg>

                    <!-- Content overlay -->
                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 2;">
                        <div style="background-color: rgba(255,255,255,0.9); padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); width: 80%;">
                            <div style="font-size: 16px; color: #333; font-weight: bold;">
                                BACK SIDE
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#frontCardPreview').html(frontHtml);
            $('#backCardPreview').html(backHtml);

            // Update status badge
            if (entityData && entityData.status === 'approved') {
                $('#entityStatusBadge')
                    .text('Approved')
                    .attr('class', 'status-badge status-approved');
            }

            // Button state is controlled by the profile confirmation check in document.ready

            console.log('Simple preview rendered');
        }

        // Function to show custom approval loading overlay
        function showCustomApprovalLoadingOverlay(percent) {
            $('#customApprovalLoadingOverlay').css({
                display: 'flex'
            });
            updateCustomApprovalProgress(percent || 0);
        }

        // Function to hide custom approval loading overlay
        function hideCustomApprovalLoadingOverlay() {
            $('#customApprovalLoadingOverlay').hide();
        }

        // Function to update custom approval progress
        function updateCustomApprovalProgress(percent) {
            // Update circular progress bar
            const circumference = 2 * Math.PI * 45; // 45 is the radius of the circle
            const offset = circumference - (percent / 100) * circumference;
            $('.progress-bar').css('stroke-dashoffset', offset);

            // Update text
            $('#customApprovalProgressText').text(Math.round(percent) + '%');
        }

        // Function to update progress bar
        function updateProgressBar(percentage, statusText) {
            updateCustomApprovalProgress(percentage);
        }

        // Function to handle single file progress
        function single_file_progress(percentage) {
            if (percentage == 100) {
                in_progress_promises--;
                if (in_progress_promises == 0) {
                    current_percentage = percentage;
                }
            } else {
                if (current_percentage < percentage) {
                    current_percentage = percentage;
                }
            }
            return false;
        }

        // Function to approve ID card
        function approveIdCard() {
            Swal.fire({
                title: 'Approve ID Card?',
                text: "Are you sure you want to approve this ID card?",
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, approve it',
                cancelButtonText: 'Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Hide approve button
                    $('#approveBtn').prop('disabled', true);

                    // Show custom loading overlay
                    showCustomApprovalLoadingOverlay(0);

                    in_progress_promises = 2;
                    current_percentage = 0;

                    setTimeout(() => {
                        updateCustomApprovalProgress(10);
                        processCardImages();
                    }, 500);
                } else {
                    // Reset the button state if the user cancels
                    $('#approveBtn').prop('disabled', false).html('<i class="fa fa-check"></i> APPROVE');
                }
            });
        }

        // Function to process card images
        function processCardImages() {
            const frontEl = document.querySelector('#frontCardPreview');
            const backEl = document.querySelector('#backCardPreview');

            if (!frontEl || !backEl) {
                updateProgressBar(100, 'Error: Card preview not found');
                showNotification('error', 'Card preview not found.');
                resetApprovalUI();
                return;
            }

            // Update progress to show we're starting to process the front image
            updateProgressBar(15, 'Processing front side of ID card...');

            // Process front image first
            processImage(frontEl, 'front.png')
                .then(frontUrl => {
                    // Update progress to show we're starting to process the back image
                    updateProgressBar(40, 'Processing back side of ID card...');

                    // Then process back image
                    return processImage(backEl, 'back.png')
                        .then(backUrl => {
                            // Both images processed successfully
                            updateProgressBar(70, 'Uploading ID card to server...');
                            generateCard(frontUrl, backUrl);
                        });
                })
                .catch(error => {
                    updateProgressBar(100, 'Error: Failed to process images');
                    showNotification('error', error.message || 'Failed to process images');
                    resetApprovalUI();
                });
        }

        // Function to process image
        function processImage(element, filename) {
            return new Promise((resolve, reject) => {
                // Use html2canvas with better options for more reliable rendering
                const options = {
                    useCORS: true,           // Enable CORS for images
                    allowTaint: true,        // Allow tainted canvas
                    backgroundColor: null,    // Transparent background
                    scale: 2                 // Higher quality
                };

                html2canvas(element, options).then(canvas => {
                    canvas.toBlob(blob => {
                        if (!blob) {
                            reject(new Error(`Failed to render ${filename}`));
                            return;
                        }
                        uploadToS3(blob, filename)
                            .then(resolve)
                            .catch(reject);
                    }, 'image/png');
                }).catch(err => {
                    console.error('Error in html2canvas:', err);
                    reject(err);
                });
            });
        }

        // Function to upload to S3
        function uploadToS3(blob, filename) {
            return new Promise((resolve, reject) => {
                // Update progress to show we're getting a signed URL
                if (filename === 'front.png') {
                    updateProgressBar(20, 'Getting upload URL for front side...');
                } else {
                    updateProgressBar(45, 'Getting upload URL for back side...');
                }

                $.ajax({
                    url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                    type: 'POST',
                    data: {
                        file_type: blob.type,
                        filename: filename,
                        folder: 'idcards'
                    },
                    success: function(response) {
                        try {
                            const { path, signedUrl } = JSON.parse(response);

                            // Update progress to show we're uploading
                            if (filename === 'front.png') {
                                updateProgressBar(25, 'Uploading front side to server...');
                            } else {
                                updateProgressBar(50, 'Uploading back side to server...');
                            }

                            $.ajax({
                                url: signedUrl,
                                type: 'PUT',
                                headers: {
                                    "Content-Type": blob.type,
                                    "x-amz-acl": "public-read"
                                },
                                processData: false,
                                data: blob,
                                xhr: function() {
                                    const xhr = $.ajaxSettings.xhr();
                                    xhr.upload.onprogress = function(e) {
                                        if (e.lengthComputable) {
                                            const progress = (e.loaded / e.total * 100) | 0;
                                            // Update progress based on which file we're uploading
                                            if (filename === 'front.png') {
                                                // Front image progress from 25% to 40%
                                                const adjustedProgress = 25 + (progress * 0.15);
                                                updateProgressBar(adjustedProgress, 'Uploading front side: ' + progress + '%');
                                            } else {
                                                // Back image progress from 50% to 65%
                                                const adjustedProgress = 50 + (progress * 0.15);
                                                updateProgressBar(adjustedProgress, 'Uploading back side: ' + progress + '%');
                                            }
                                        }
                                    };
                                    return xhr;
                                },
                                success: () => {
                                    if (filename === 'front.png') {
                                        updateProgressBar(40, 'Front side uploaded successfully');
                                    } else {
                                        updateProgressBar(65, 'Back side uploaded successfully');
                                    }
                                    resolve(path);
                                },
                                error: (err) => {
                                    updateProgressBar(100, 'Error: Failed to upload to server');
                                    reject(new Error('Failed to upload to S3'));
                                }
                            });
                        } catch (err) {
                            updateProgressBar(100, 'Error: Invalid server response');
                            reject(new Error('Invalid signed URL response'));
                        }
                    },
                    error: () => {
                        updateProgressBar(100, 'Error: Failed to get upload URL');
                        reject(new Error('Failed to get signed URL'));
                    }
                });
            });
        }

        // Function to generate card
        function generateCard(frontUrl, backUrl) {
            updateProgressBar(75, 'Finalizing ID card approval...');


            if (entityData && entityData.sa_id) {
                console.log('Student ID from sa_id:', entityData.sa_id);
            }

            if (!student_id) {
                console.error('Missing student_id for approval');
                updateProgressBar(100, 'Error: Missing student ID');
                showNotification('error', 'Missing student ID for approval');
                resetApprovalUI();
                return;
            }

            if (!order_id) {
                console.error('Missing order_id for approval');
                updateProgressBar(100, 'Error: Missing order ID');
                showNotification('error', 'Missing order ID for approval');
                resetApprovalUI();
                return;
            }

            // Try to fetch template_id from the URL if it's still missing
            if (!template_id) {
                // Check if we can extract it from the URL
                const urlParams = new URLSearchParams(window.location.search);
                const urlTemplateId = urlParams.get('template_id');
                if (urlTemplateId) {
                    template_id = urlTemplateId;
                    console.log('Using template_id from URL:', template_id);
                } else {
                    console.warn('Template ID is missing but proceeding with approval');
                }
            }

            // Define approval data
            const approvalData = {
                staffData: student_id,
                type: id_card_for,
                frontUrl: frontUrl,
                backUrl: backUrl,
                order_id: order_id
            };

            $.ajax({
                url: '<?php echo site_url("parent_controller/aprrove_idcards_templates") ?>',
                type: 'POST',
                data: approvalData,
                success: function(response) {
                    try {
                        const result = JSON.parse(response);

                        if (result.success) {
                            updateProgressBar(100, 'ID card approved successfully!');
                            showNotification('success', 'ID card approved successfully');

                            // Update the status badge
                            $('#entityStatusBadge')
                                .text('Approved')
                                .attr('class', 'status-badge status-approved');

                            // Disable approve button
                            $('#approveBtn').prop('disabled', true).html('APPROVED <i class="fa fa-check"></i>');

                            // Hide loading overlay after a delay
                            setTimeout(() => {
                                hideCustomApprovalLoadingOverlay();
                            }, 1000);
                        } else {
                            updateProgressBar(100, 'Error: ' + (result.message || 'Failed to approve ID card'));
                            showNotification('error', result.message || 'Failed to approve ID card');
                            resetApprovalUI();
                        }
                    } catch (e) {
                        console.error('Error parsing response:', e);
                        updateProgressBar(100, 'Error: Failed to parse server response');
                        showNotification('error', 'Failed to parse server response');
                        resetApprovalUI();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Card generation error:', error);
                    console.error('XHR response:', xhr.responseText);
                    updateProgressBar(100, 'Error: Failed to generate card');
                    showNotification('error', 'Failed to generate card: ' + error);
                    resetApprovalUI();
                }
            });
        }

    function generate_qr_code(qrdata, qr_size, callback){
        $.ajax({
            url: "<?php echo site_url('parent_controller/generate_qr_code_for_idcards') ?>",
            data: { qrdata: qrdata, qr_size:qr_size},
            success: function (base64img) {
                callback(base64img);
            }
        });
    }
        // Function to reset approval UI
        function resetApprovalUI() {
            // Reset button state based on entity status
            if (entityData && entityData.status === 'approved') {
                $('#approveBtn').prop('disabled', true).html('APPROVED <i class="fa fa-check"></i>');
            } else {
                $('#approveBtn').prop('disabled', false).html('<i class="fa fa-check"></i> APPROVE');
            }

            // Hide loading overlay
            hideCustomApprovalLoadingOverlay();
        }

        // Function to show notification
        function showNotification(type, message) {
            Swal.fire({
                icon: type,
                title: type === 'success' ? 'Success' : 'Error',
                text: message,
                timer: 3000,
                timerProgressBar: true
            });
        }

      
        $(document).ready(function() {
       
            $('#approveBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> LOADING...');

          
            $.ajax({
                url: '<?php echo site_url("parent_controller/getProfileCompletionStatus") ?>',
                type: 'POST',
                success: function(response) {
                    console.log('Profile completion raw response:', response);
                    console.log('Response type:', typeof response);

                    try {
                        // Try to parse response as JSON
                        let result;
                        if (typeof response === 'string') {
                            // Check if response looks like JSON
                            if (response.trim().startsWith('{') || response.trim().startsWith('[')) {
                                result = JSON.parse(response);
                            } else {
                                throw new Error('Response is not valid JSON format: ' + response.substring(0, 100));
                            }
                        } else {
                            result = response;
                        }
                       
                        if (result.has_idcard === false) {
                            // Hide student ID card section and show appropriate message
                            hideStudentIdCardSection();
                            return;
                        }

                        if (result.profile_status == 'locked' && result.profile_confirmed == 'No') {
                            Swal.fire({
                                title: 'Warning',
                                text: 'Profile Edit is locked. Kindly contact the school admin for further clarification.',
                                icon: 'warning',
                                confirmButtonText: 'OK',
                                allowOutsideClick: false
                            });
                            $('#approveBtn').prop('disabled', true).html('<i class="fa fa-check"></i> APPROVE');
                        } else if (result.profile_confirmed == 'Yes' && result.profile_status == 'locked') {
                            $('#approveBtn').prop('disabled', false).html('<i class="fa fa-check"></i> APPROVE');
                            $('#approveBtn').off('click').on('click', function() {
                                approveIdCard();
                            });
                        } else if ((
                            (typeof result.profile_status === 'string' && result.profile_status.toLowerCase() === 'unlocked') ||
                            result.profile_status === '' ||
                            result.profile_status === null ||
                            (typeof result.profile_status === 'string' && result.profile_status.toLowerCase() === 'unlock')
                        ) && result.profile_confirmed === 'No') {
                            Swal.fire({
                                title: 'Warning',
                                text: 'You have not confirmed the student profile details, kindly we request you to confirm it and come back to approve idcard',
                                icon: 'warning',
                                confirmButtonText: 'Go to Profile',
                                allowOutsideClick: false
                            }).then((swalResult) => {
                                if (swalResult.isConfirmed) {
                                    navigateProfile();
                                }
                            });
                            $('#approveBtn').prop('disabled', true).html('<i class="fa fa-check"></i> APPROVE');
                        } else if(result.idcard_status == 'in review' || result.idcard_status == 'modify'){
                            $('#approveBtn').prop('disabled', false).html('<i class="fa fa-check"></i> APPROVE');
                            $('#approveBtn').off('click').on('click', function() {
                                approveIdCard();
                            });
                        }
                    } catch (e) {
                        console.error('Error parsing profile completion response:', e);
                        console.error('Raw response:', response);
                        console.error('Response type:', typeof response);
                        $('#approveBtn').prop('disabled', true).html('<i class="fa fa-check"></i> APPROVE');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error checking profile confirmation status:');
                    console.error('Status:', status);
                    console.error('Error:', error);
                    console.error('Response Text:', xhr.responseText);
                    $('#approveBtn').prop('disabled', true).html('<i class="fa fa-check"></i> APPROVE');
                }
            });

            if ($('form').length > 0) {
                $('form').parsley();
            }

            if (entityData) {
                renderPreview(entityData);
            } else {
                console.log('No entity data available for rendering student ID card');
                hideStudentIdCardSection();
            }
        });

        function navigateProfile(){
            window.location.href = "<?= site_url('parent_controller/profile') ?>";
        }


    function downloadIdcard(element) {
        // Get file path and name from data attributes
        var filePath = element.getAttribute('data-file-path');
        var fileName = element.getAttribute('data-file-name') || 'IDCard.jpg';

        if (!filePath) {
            console.error('File path not found');
            alert('File path not found');
            return;
        }

        // Create a form to send POST data
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo site_url("parent_controller/download_id_card_as_pdf"); ?>';
        form.style.display = 'none';

        // Add file_url parameter
        var fileUrlInput = document.createElement('input');
        fileUrlInput.type = 'hidden';
        fileUrlInput.name = 'file_url';
        fileUrlInput.value = filePath;
        form.appendChild(fileUrlInput);

        // Add file_name parameter
        var fileNameInput = document.createElement('input');
        fileNameInput.type = 'hidden';
        fileNameInput.name = 'file_name';
        fileNameInput.value = fileName;
        form.appendChild(fileNameInput);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    </script>