<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>
<?php $this->load->helper('reports_datatable');
echo filters_dropdown(); ?>
<div class="card-body" id="filtersContainer">
  <div class="row align-items-end">
    <div class="col-md-3 form-group">
      <p>Select Date Range</p>
      <div id="reportrange" class="dtrange arrow-mark" style="width: 100%; height:40px; padding: 5px 0px 5px 10px;">
        <span></span>
        <input type="hidden" id="from_date">
        <input type="hidden" id="to_date">
      </div>
    </div>

    <div class="col-md-3 form-group" id="classSection">
      <p>Select Reconcilation Type</p>
      <?php
      $dataArr = array('1' => 'Non Reconciled', '2' => 'Reconciled', '3' => 'Failed');
      $array = array();
      foreach ($dataArr as $key => $val) {
        $array[$key] = $val;
      }
      echo form_dropdown("reconcilation", $array, set_value("reconcilation", $reconcilation), "id='reconcilation' class='form-control arrow-mark'");
      ?>
    </div>

    <div class="col-md-6 form-group d-flex justify-content-end">
      <input type="button" onclick="get_report_reconciled()" name="search" id="search" class="btn btn-primary" value="Get Report">
    </div>
  </div>
</div>
<?php $this->load->helper('reports_datatable'); echo progress_bar(); ?>
<!-- No data state -->
<?php $this->load->helper('reports_datatable'); echo no_data_message(); ?>


<div class="card-body">
  <div id="printArea_summary">
    <!--  <div id="print_summary" style="display: none" class="text-center">
                <h4>Daily Fee Report</h4>
                <h5>From <span id="fromDate_summary"></span> To <span id="toDate_summary"></span></h5>
              </div> -->
    <!-- <ul class="panel-controls pull-right" id="exportIcon_summary" style="display: none" >
                <button id="stu_print" class="btn btn-danger" onClick="print_daily_summary();"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button> -->
    <!--  <a style="margin-left:3px;" onclick="exportToExcel_summary()" class="btn btn-primary pull-right"><span class="fa fa-file-text-o"></span> Export</a> -->
    <!-- </ul> -->
    <div style="clear: both"></div>
    <div class="panel-body reconciled_summary  table-responsive hidden-xs" style="padding: 0">
    </div>
  </div>
</div>

</div>
</div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD.MM.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));


  function get_report_reconciled() {
    $('#search').prop('disabled', true).val('Please wait...');
    $(".progress").show();
    $(".no-data-state").hide();
    $('.reconciled_summary').hide();
    $('#exportIcon_summary').hide();
     $('#print_summary').hide();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var reconcilation = $('#reconcilation').val();
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/fee_reonciled_data'); ?>',
      type: 'post',
      data: {'from_date':from_date, 'to_date':to_date,'reconcilation':reconcilation},
      success: function(data) {
        var reconciled_tx = JSON.parse(data);
        console.log(reconciled_tx);
        $(".progress").hide();
        $('#search').prop('disabled', false).val('Get Report');

        // Check if no data is available
        if (!reconciled_tx || reconciled_tx.length === 0) {
          $('#exportIcon_summary').hide();
          $('#print_summary').hide();
          $('.reconciled_summary').hide();
          $(".no-data-state").show();
          return;
        }

        $('#exportIcon_summary').show();
        $('#print_summary').show();
        $(".no-data-state").hide();
        $('.reconciled_summary').show();
        $('.reconciled_summary').html(construct_summary_table(reconciled_tx));

        const reportName=`Reconciled_summary ${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

      $('#main_table_id').DataTable( {
        ordering:false,
        paging : true,
        responsive: true,
				searching:true,
        pageLength:10,
        scrollY: '500px',
        scrollX: true,
        scrollCollapse: true,
				dom: '<"d-flex justify-content-between align-items-center mb-3"<"d-flex align-items-center flex-nowrap"l><"d-flex align-items-center flex-nowrap"fB>>rt<"d-flex justify-content-between align-items-center"ip>',
				buttons: [
          {
					extend: 'print',
					text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
					filename: reportName,
					exportOptions: {
						columns: ':not(:last-child)'
					}
					},
					{
					extend: 'excelHtml5',
					text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel.svg', [], true); ?> Excel</button>`,
					filename: reportName,
					exportOptions: {
						columns: ':not(:last-child)'
					}
					}
				]
        	});

        const $filter = $('#main_table_id_filter');
        const $input = $filter.find('input');

        // Remove "Search:" text
        $filter.find('label').contents().filter(function () {
          return this.nodeType === 3; 
        }).remove();

        $input.attr('placeholder', 'Search');
        $filter.addClass('custom-search-box');
        $filter.find('label').wrapInner('<div class="search-box"></div>');
        $filter.find('.search-box').prepend('<i class="bi bi-search"></i>');

        $(document).ready(function(){
          $('.paidDate').datepicker({
            format: 'dd-mm-yyyy',
            "autoclose": true
          });
        });
      },
      error: function(xhr, status, error) {
        $(".progress").hide();
        $('#search').prop('disabled', false).val('Get Report');

      }
    });
  }

  function construct_summary_table(recon) {
    var html = '';

    // Calculate totals
    var total_amount = 0;
    var totalConcession = 0;
    for(var k in recon){
      total_amount += parseFloat(recon[k].amount_paid);
      totalConcession += parseFloat(recon[k].concession_amount);
    }

    // Get school name
    var schoolName = '<?php echo htmlspecialchars($this->settings->getSetting("school_name")); ?>';

    // Create summary table
    html +='<div class="row mb-3">';
    html +='<div class="col-md-12">';
    html +='<table class="table table-bordered" id="summary_totals_table" style="margin-bottom: 20px; text-align: center;">';
    html +='<thead>';
    html +='<tr>';
    html +='<th colspan="2" style="text-align: center !important; padding: 12px; font-weight: 600!important;font-size:20px!important; vertical-align: middle;">';
    html += schoolName;
    html +='<br>';
    html +='<span style="font-weight: 500!important;font-size:14px!important;">Reconciled Report</span>';
    html +='</th>';
    html +='</tr>';
    html +='</thead>';
    html +='<tbody>';
    html +='<tr>';
    html +='<td style="text-align: left !important; padding: 12px; font-weight: 500!important;font-size:15px!important;">Summary</td>';
    html +='</tr>';
    html +='<tr>';
    html +='<td style="text-align: left !important; font-weight: bold; padding: 15px; font-size: 14px; vertical-align: middle;">Total Amount</td>';
    html +='<td style="text-align: center !important; font-weight: bold; padding: 15px; font-size: 16px; vertical-align: middle;">'+numberToCurrency(total_amount)+'</td>';
    html +='</tr>';
    html +='<tr>';
    html +='<td style="text-align: left !important; font-weight: bold; padding: 15px; font-size: 14px; vertical-align: middle;">Total Concession</td>';
    html +='<td style="text-align: center !important; font-weight: bold; padding: 15px; font-size: 16px; vertical-align: middle;">'+numberToCurrency(totalConcession)+'</td>';
    html +='</tr>';
    html +='</tbody>';
    html +='</table>';
    html +='</div>';
    html +='</div>';

    // Create main data table
    html +='<table class="table table-bordered" id="main_table_id">';
    html +='<thead>';
    html +='<tr>';
    html +='<th>#</th>';
    html +='<th>Fee Type</th>';
    html +='<th>Admission No.</th>';
    html +='<th>Student Name</th>';
    html +='<th>Class Section</th>';
    html +='<th>Receipt No.</th>';
    html +='<th>DD/Cheque Date</th>';
    html +='<th>DD/Cheque No.</th>';
    html +='<th>Bank Details</th>';
    html +='<th>Amount</th>';
    html +='<th>Concession</th>';
    html +=' <th class="print_disable">Action</th>';
    html +='</tr>';
    html +='</thead>';
    html +='<tbody>';
    for (var i = 0; i < recon.length; i++) {
      html +='<tr>';
      html +='<td>'+(i+1)+'</td>';
      html +='<td>'+recon[i].blueprint_name+'</td>';
      html +='<td>'+recon[i].admission_no+'</td>';
      html +='<td>'+recon[i].stdName+'</td>';
      html +='<td>'+recon[i].class_section+'</td>';
      html +='<td>';
      if (recon[i].reconciliation_status == '3') {
        var url = '<?php echo site_url('feesv2/fees_collection/fee_reciept_view/') ?>'+recon[i].trnsId+'/'+'1';
         html +='<a target="_blank" href="'+url+'">'+recon[i].receipt_number+'</a>';
      }else{
         html += recon[i].receipt_number;
      }
      html +='</td>';
      html +='<td>'+recon[i].cheque_or_dd_date+'</td>';
      html +='<td>'+recon[i].cheque_dd_nb_cc_dd_number+'</td>';
      html +='<td>'+recon[i].payment_type+'</td>';
      html +='<td>'+numberToCurrency(parseFloat(recon[i].amount_paid) + parseFloat(recon[i].fine_amount) )+'</td>';
      html +='<td>'+numberToCurrency(parseFloat(recon[i].concession_amount))+'</td>';
      html +='<td class="print_disable">';
      if (recon[i].reconciliation_status == '1') {
        var url = '<?php echo site_url('feesv2/fees_collection/fee_reciept_view/') ?>'+recon[i].trnsId+'/'+'1';
        html +='<a class="btn btn-warning" data-toggle="modal" data-target="#summary_'+recon[i].trnsId+'" data-placement="top" data-toggle="tooltip" id="recon_'+recon[i].trnsId+'" data-id="'+recon[i].trnsId+'" data-backdrop="static" data-keyboard="false" data-original-title="Reconciliation"><i class="fa fa-exchange"></i></a>';
      }
      html +='</td>';
      html +='</tr>';

      html +=`<div id="summary_${recon[i].trnsId}" class="modal fade" role="dialog">
            <div class="modal-dialog">
                <div class="panel panel-default new-panel-style_3">
                    <div class="panel-heading new-panel-heading">
                        <h3 class="panel-title">Check the details and click Submit</h3>
                    </div>
                    <div class="panel-body">
                        <div class="col-md-12">
                            <div class="col-md-6">
                                <label class="control-label">Reciept No: ${recon[i].receipt_number} </label>
                            </div>
                            <div class="col-md-6">
                                <label class="control-label">Amount: ${recon[i].amount_paid} </label>
                            </div>
                            <div class="col-md-6">
                                <label class="control-label">Student Name: ${recon[i].stdName} </label>
                            </div>
                            <div class="col-md-6">
                                <label class="control-label">Class / Section: ${recon[i].class_section} </label>
                            </div>
                            <div class="col-md-6">
                              <label class="control-label">Payment Type </label> : ${recon[i].payment_type} </label>
                            </div>
                        </div>
                    </div>
                    <div class="panel panel-default new-panel-style_3">
                        <div class="panel-body">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="col-md-6">Bank Paid Date: </label>
                                    <div class="col-md-4">
                                        <div class="input-group" > 
                                            <input type="text" value="<?php echo date('d-m-Y') ?>"  class="form-control paidDate" id="paidDate_${recon[i].trnsId}" name="bank_paid_date" >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="cohort_student_id" value="${recon[i].chortStudent_id}">
                    <div class="panel-footer new-footer">
                        <button id="submitbutton" onclick="submit_by_id(${recon[i].trnsId})" class="btn btn-info">Success</button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">Cancel</button>
                        <button id="submitbutton"  onclick="faild_by_id('${recon[i].trnsId}','${recon[i].receipt_number}')" class="btn btn-danger">Fail</button>
                    </div>
                </div>
            </div>
          </div>`;
    }
    html +='</tbody>';
    html +='</table>';

    return html;
  }

  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }

 
</script>

<script type="text/javascript">
    // $(document).ready(function(){
    //     $(".paidDate").on("keyup change", function() {
    //        $("#submitbutton").prop('disabled',false);
    //     });
    // });
    function submit_by_id($feeId) {
    var feeId = $feeId;
    var bank_date= $('#paidDate_'+$feeId).val();
    if (bank_date =='') {
      return false;
    }
    var cohort_student_id = $('#cohort_student_id').val();
    $('#submitbutton').attr('disabled','disabled').html('Please wait....');
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_collection/reconsilation_fee'); ?>',
        type: "post",
        data:{'feeId':feeId,'bank_date':bank_date,'cohort_student_id':cohort_student_id},
        success: function (data) {
            // console.log(data);
            if (data.length=='1') {
             $('#recon_'+$feeId).attr('disabled','disabled');
            }else{
              $('#recon_'+$feeId).attr('disabled','disabled');
            }
            $('#summary_'+$feeId).modal('hide');

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Reconciliation completed successfully.',
                confirmButtonText: 'OK'
            }).then((result) => {
                if (result.isConfirmed) {
                    get_report_reconciled();
                }
            });
        },
        error: function (err) {
            console.log(err);
            $('#submitbutton').attr('disabled', false).html('Success');
        }
    });
}

function faild_by_id(feeId, feeNumber) {
 var cohort_student_id = $('#cohort_student_id').val();
  bootbox.prompt({
      inputType:'text',
      placeholder: 'Enter Remarks',
      buttons: {
          confirm: {
              label: 'Yes',
              className: 'btn-success'
          },
          cancel: {
              label: 'No',
              className: 'btn-danger'
          }
      },
      title: "Reconciliation failed for Receipt: " + feeNumber +". Are you Sure?", 
      callback: function (remarks) {
        if (remarks=='') {
          return false;
        }
        if(remarks) {        
          $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/reconsilation_failed'); ?>',
            type: 'post',
            data: {'feeId' : feeId,'remarks':remarks,'cohort_student_id':cohort_student_id},
            success:function(data){
              if(data){
                 location.reload();
              new PNotify({
                  title: 'Success',
                  text: 'Successfully updated',
                  type: 'success',
                });
              } else {
               new PNotify({
                  title: 'Error',
                  text: 'Something went wrong',
                  type: 'Error',
                });
              }
            }
          });
        }
      }
  });
}

function print_daily_summary(){
    var restorepage = document.body.innerHTML;
    $('#print_summary').css('display','block');
    $('#exportIcon_summary').css('display','none');
    $('.print_disable').css('display','none');
    var printcontent = document.getElementById('printArea_summary').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }
</script>
<style>
  div.dt-buttons>.dt-button{
    all:unset;
    background:none;
    border: none;
    background-color: none;
  }
  div.dt-buttons:hover>.dt-button:hover{
    all:unset;
    background:none;
    border: none;
    background-color: none;
  }
  p{
    margin-bottom: 0.5px;
  }
   .widthadjust{
  width:600px;
  margin:auto;
  }

  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }
  .dataTables_scrollBody{
    margin-top: -13px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .form-horizontal .control-label{
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;

  }
  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}
	}

	/* Sticky header implementation */
	#main_table_id {
		position: relative;
	}

	#main_table_id thead th {
		position: sticky !important;
		top: 0;
		background-color: #EFECFD !important;
		z-index: 11 !important;
	}

	/* Big scrollbar for DataTable wrapper */
	.dataTables_scrollBody {
		overflow-x: auto !important;
		overflow-y: auto !important;
	}

	.dataTables_scrollBody::-webkit-scrollbar {
		height: 14px;
		width: 14px;
	}

	.dataTables_scrollBody::-webkit-scrollbar-thumb {
		background: #cbd5e1;
		border-radius: 8px;
	}

	.dataTables_scrollBody::-webkit-scrollbar-track {
		background: #f1f5f9;
		border-radius: 8px;
	}

	/* For Firefox */
	.dataTables_scrollBody {
		scrollbar-width: thick;
		scrollbar-color: #cbd5e1 #f1f5f9;
	}

	/* DataTable length selector styling */
	.dataTables_length {
		white-space: nowrap !important;
		margin-right: 20px !important;
		min-width: 200px !important;
	}

	.dataTables_length select {
		margin: 0 5px !important;
		min-width: 60px !important;
	}

	.dataTables_length label {
		display: flex !important;
		align-items: center !important;
		white-space: nowrap !important;
		font-size: 14px !important;
	}
</style>