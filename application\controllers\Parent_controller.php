<?php

class Parent_controller extends CI_Controller
{

	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->model('parent_model');
		$this->load->model('ptm_model');
		$this->config->load('form_elements');
		$this->load->library('filemanager');
		$this->load->library('payment');
		$this->load->library('payment_payu');
		$this->load->library('payment_jodo');
		$this->load->model('HomeworkModel');
		$this->load->model('flash_model');
		$this->load->model('feesv2/fees_student_model');
		$this->load->model('feesv2/fees_collection_model');
		
		$this->load->model('student/Certificates_Model');
		$this->load->library('fee_library');
		// $this->load->model('feesv2/fees_cohorts_model'); // not requried 
		$this->load->model('timetablev2/template_model', 'template_model');
		$this->load->helper('text');
		$this->load->model('student_wallet_model');
		$this->load->model('classroom_chronicles/Classroom_chronicles_model');
		$this->load->model('academics/lessonPlan_model', 'lessonplan_model');
		$this->load->model('staff/Staff_Model');
		$this->load->model('student/student_analytics_model');
		$this->load->model('calenderevents_model');
		$this->yearId =  $this->acad_year->getAcadYearId();
		$this->load->model('student_exit_flow/Student_exit_flow_model');
        $this->load->model('role');
		$this->load->model('idcards/Idcards_model');
        $this->load->model('attendance_day_v2/Attendance_day_v2_model','attendance_day_v2_model');

		$isforceChangePasswordForParentsEnabled=$this->settings->getSetting("enable_force_change_password_for_parents");
		if($isforceChangePasswordForParentsEnabled){
			$is_reset_password_required = $this->parent_model->check_parent_reset_password_required();
			if(!empty($is_reset_password_required)){
				if ($is_reset_password_required->reset_password_required == 1) {
					redirect('auth/change_password');
				}
			}
		}
	}

	// profile
	public function profile($callFrom = ''){
		$data['callFrom'] = $callFrom;
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_id'] = $studentId;
		$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
		if(!empty($data['studentData']->category)){
			$data['studentData']->category = $this->settings->getSetting('category')[$data['studentData']->category];
		}
		// echo "<pre>"; print_r($data['studentData']); die();
		$stops = $this->parent_model->getFeesStops();
		$data['stops'] = array();
		foreach ($stops as $key => $stop) {
			$data['stops'][$stop->id] = $stop->name;
		}
		if ($data['studentData']->is_placeholder == 1) {
			$data['studentData']->sectionName = '';
		}

		//Get Student details
		$student_address_types = $this->settings->getSetting('student_address_types', 1, 1);

		$sAddress = [];
		if (!empty($student_address_types)) {
			foreach ($student_address_types as $key => $address) {
				$sAddress[$address] = $this->parent_model->getStudent_Address_Details($studentId, $key);
			}
		} else {
			$sAddress = array();
		}

		//Get Father details
		// $profile_display_father = $this->settings->getSetting('parent_profile_display_father');
		$father_address_types = $this->settings->getSetting('father_address_types');

		$data['fatherData'] = $this->parent_model->getFatherDetails($studentId);
		$fAddress = [];
		if (!empty($father_address_types)) {
			foreach ($father_address_types as $key => $address) {
				$fAddress[$address] = $this->parent_model->getFather_Address_Details($data['fatherData']->id, $key);
			}
		} else {
			$fAddress = array();
		}


		//Get Mother details
		$mother_address_types = $this->settings->getSetting('mother_address_types');
		$data['motherData'] = $this->parent_model->getMotherDetails($studentId);
		$mAddress = [];
		if (!empty($mother_address_types)) {
			foreach ($mother_address_types as $key => $address) {
				$mAddress[$address] = $this->parent_model->getFather_Address_Details($data['motherData']->id, $key);
			}
		} else {
			$mAddress = array();
		}

		$data['studentAddress'] = $sAddress;
		$data['fatherAddress'] = $fAddress;
		$data['motherAddress'] = $mAddress;

		// $guardian_fields = $this->settings->getSetting('parent_profile_display_guardian');
		$data['show_guardian'] = 0;
		if($this->settings->isProfile_profile_enabled('GUARDIAN_PHOTO') || $this->settings->isProfile_profile_enabled('GUARDIAN_NAME') || $this->settings->isProfile_profile_enabled('GUARDIAN_CONTACT_NO') || $this->settings->isProfile_profile_enabled('GUARDIAN_EMAIL')){
		// if ($guardian_fields && $guardian_fields->display == 1) {
			$data['show_guardian'] = 1;
			$data['guardianData'] = $this->parent_model->getGuardianDetails($studentId);
			// echo "Fields: <pre>"; print_r($data['guardianData']); die();
		}

		$data['family_picture_url'] = $data['studentData']->family_picture_url;

		$data['electives'] = $this->parent_model->getElectives($studentId);

		$data['canteenModuleEnabled'] = $this->authorization->isModuleEnabled('CANTEEN') && $this->authorization->isParentModuleEnabled('CANTEEN');
		if ($data['canteenModuleEnabled']) {
			$data['canteen'] = $this->parent_model->get_canteen_details($studentId);
		}
		$data['clsTeacherSub'] = $this->parent_model->get_class_wise_teacher_subj($data['studentData']->sectionId);
		$data['class_teacher'] = $this->parent_model->get_classTeacher_section($data['studentData']->sectionId);

		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'parent/profile/tablet_index';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/profile/mobile_index';
		} else {
			$data['main_content']    = 'parent/profile/index';
		}
		// $data['main_content']    = 'parent/profile/index';
		$this->load->view('inc/template', $data);
	}

	public function student_profile_view1(){
		$data['studentId'] = $this->parent_model->getStudentIdOfLoggedInParent();
		$enabled_config_fields = (array) $this->settings->getSetting('student_profile_edit_columns');
		$data['showEditfields'] = $enabled_config_fields;
		$data['columns'] = $this->db->list_fields('student_admission');
		$data['acad_years'] = $this->acad_year->getAllYearData();
    	$data['acad_year_id'] = $this->acad_year->getAcadYearId();
		// echo '<pre>';print_r($data['columns'] );die();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'parent/profile/tablet_index';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/profile/mobile_index';
		} else {
			$data['main_content']    = 'parent/profile_new/index';
		}
		$this->load->view('inc/template', $data);
	}

	public function removeSubmittedTaskFiles()
	{
		$lp_tasks_students_id = $_POST['lp_tasks_students_id'];
		echo $this->parent_model->removeSubmittedTaskFiles($lp_tasks_students_id);
	}

	// Fee Details 

	public function display_fee_blueprints()
	{
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$fee_blueprints = $this->parent_model->get_published_blueprints($student_id);

		$data['transport_select_for_parents'] = $this->settings->getSetting('transport_select_for_parents');
		if ($data['transport_select_for_parents']) {
			$data['transport'] = $this->parent_model->get_blueprint_transport_enabled_id($student_id);
		}

		$data['transport_assinged'] = $this->parent_model->get_transport_blueprints_is_assinged($student_id);

		// echo "<pre>"; print_r($fee_blueprints);
		// echo "<pre>"; print_r($data['transport_assinged']);
		// die();
		//Commenting this code to disable disabling the button if transaction is in progress.
		// foreach ($fee_blueprints as $key => &$val) {
		// 	$val->trans_status =  $this->parent_model->transaction_status($val->std_sch_id);
		// }
		$data['fee_blueprints'] = $fee_blueprints;
		$data['student_id'] = $student_id;
		$data['main_content'] = 'parent/feesv2/index';
		$data['path_prefix'] = $this->filemanager->getFilePath('');

		//For Help Content
		// $this->load->helper('chatdata_helper');
		// $this->load->library('filemanager');
		// $data['chatData'] = getChatData('Parent Fees');
		$data['type'] = 'Parent Fees';
		$data['back_url'] = site_url('parent_controller/display_fee_blueprints');

		$this->load->view('inc/template', $data);
	}

	public function fee_details()
	{
		$this->load->helper('fees_helper');
		$input = $this->input->post();

		if (!isset($input['blueprint_id'])) {
			redirect('auth/login');
		}

		$data['blueprint_id'] = $input['blueprint_id'];
		$data['student_id'] = $input['student_id'];
		$data['std_sch_id'] = $input['std_sch_id'];
		$data['blueprint_name'] = $input['blueprint_name'];
		$data['cohort_student_id'] = $input['cohort_student_id'];
		$data['concession_remarks'] = $this->parent_model->get_concession_remarksby_cohort_std_id($data['cohort_student_id']);
		$data['adjustment_remarks'] = $this->parent_model->get_adjustment_remarksby_cohort_std_id($data['cohort_student_id']);
		$fee_blueprints = $this->parent_model->get_published_blueprints_status($data['student_id'], $data['blueprint_id']);
		$classId = $this->parent_model->get_student_class_id_by_acadyearwise($data['student_id'],$fee_blueprints->acad_year_id);
		$data['online_status'] = $fee_blueprints;
		$data['fee_amount'] = $this->parent_model->get_std_fee_amount_details($input['std_sch_id']);
		$no_of_installments = 0;
		$checked_due_crossinsIds = [];
		$today  = date('Y-m-d');
		$count = 0;
		foreach ($data['fee_amount']['installment_name'] as $key => $value) {
			if ($value['status'] != 'FULL') {
				$no_of_installments++;
			}
			if($value['ins_due_date'] < $today && $value['ins_due_date'] !='0000-00-00' && $value['ins_due_date'] !='1970-01-01'){
				array_push($checked_due_crossinsIds, $key);
			}
			if($value['ins_due_date'] > $today){
				$count++;
			}
			if($count == 0){
			   array_push($checked_due_crossinsIds, $key);
		   	}
		}
		$data['check_recon_status'] = $this->parent_model->check_recon_statusbystdId($data['student_id'], $data['std_sch_id']);
		$data['op_response_code'] = $this->parent_model->check_online_payment_progress_status($data['student_id']);
		$data['blueprint_alog'] = $this->fees_collection_model->get_fee_blueprint_alog($data['std_sch_id'], $classId);
		$data['no_of_installments'] = $no_of_installments;
		$data['checked_due_crossinsIds'] = $checked_due_crossinsIds;
		$feeView = $this->settings->isParentModuleEnabled('FEESV3');
		if ($feeView) {
			$data['main_content'] = 'parent/feesv2_installments/fee_details';
		} else {
			if ($this->mobile_detect->isTablet()) {
				$data['main_content']    = 'parent/feesv2/tablet_fee_details';
			} else if ($this->mobile_detect->isMobile()) {
				$data['main_content']    = 'parent/feesv2/mobile_fee_details';
			} else {
				$data['main_content']    = 'parent/feesv2/fee_details';
			}

			// if ($this->mobile_detect->isMobile()) {
			// 	$data['main_content']    = 'parent/feesv2/mobile_fee_details';
			// } else {
			// 	$data['main_content']    = 'parent/feesv2/fee_details';
			// }
		}
		$this->load->view('inc/template', $data);
	}

	public function fee_confirm()
	{
		$this->load->helper('fees_helper');
		$input = $this->input->post();
		$data['fees_installment_id'] = $input['fees_installment_id'];
		$data['blueprint_id'] = $input['blueprint_id'];
		$data['std_sch_id'] = $input['std_sch_id'];
		$data['blueprint_name'] = $input['blueprint_name'];
		$data['student_id'] = $input['student_id'];
		$data['cohort_student_id'] = $input['cohort_student_id'];
		$data['online_status'] = $input['online_payment'];
		$data['discount_amount'] =  $input['discount_amount'];
		$data['partial_amount'] = (isset($input['parital_amount'])) ? $input['parital_amount'] : '';
		$data['enter_amount'] =  (isset($input['enter_amount'])) ? $input['enter_amount'] : '';
		//Commenting this code to disable disabling the button if transaction is in progress.
		// $data['trans_status'] =  $this->parent_model->transaction_status($input['std_sch_id']);
		$feeView = $this->settings->isParentModuleEnabled('FEESV3');
		if ($feeView) {
			$fee_amount = $this->parent_model->get_std_fee_installments_amount_confirm($input['std_sch_id'], $input['fees_installment_id']);
			if (!empty($data['enter_amount'])) {
				$enterAmount = $data['enter_amount'] + $data['discount_amount'];
				$data['fee_amount'] = $this->_split_enter_amount_componentwise($fee_amount, $enterAmount);
			} else {
				$data['fee_amount'] = $this->_split_without_enter_amount_componentwise($fee_amount);
			}
			$data['main_content'] = 'parent/feesv2_installments/fee_collect';
		} else {
			$data['fee_amount'] = $this->parent_model->get_std_fee_amount_confirm($input['std_sch_id'], $input['fees_installment_id']);
			if ($this->mobile_detect->isTablet()) {
				$data['main_content']    = 'parent/feesv2/tablet_fee_collect';
			} else if ($this->mobile_detect->isMobile()) {
				$data['main_content']    = 'parent/feesv2/mobile_fee_collect';
			} else {
				$data['main_content']    = 'parent/feesv2/fee_collect';
			}

			// if ($this->mobile_detect->isMobile()) {
			// 	$data['main_content']    = 'parent/feesv2/mobile_fee_collect';
			// } else {
			// 	$data['main_content']    = 'parent/feesv2/fee_collect';
			// }
		}
		$data['fee_blueprints'] = $this->parent_model->get_published_blueprints_status($data['student_id'], $data['blueprint_id']);
		// echo "<pre>"; print_r($data['fee_amount']); die();
		$this->load->view('inc/template', $data);
	}
	private function _split_enter_amount_componentwise($fee_amount, $enterAmount)
	{
		foreach ($fee_amount as $insId => &$value) {
			foreach ($value as $key => $val) {
				$balance = $val->component_amount - $val->component_amount_paid -  $val->concession_amount - $val->concession_amount_paid -  $val->adjustment_amount - $val->adjustment_amount_paid;
				if ($enterAmount >= $balance) {
					$val->allocate_enter_amount = $balance;
					$enterAmount = $enterAmount - $balance;
				} else {
					$val->allocate_enter_amount =  $enterAmount;
					$enterAmount = $enterAmount - $balance;
					if ($enterAmount < 0) {
						//This condition is not possible. If so, it is a bug.
						$enterAmount = 0;
					}
				}
			}
		}
		return $fee_amount;
	}

	private function _split_without_enter_amount_componentwise($fee_amount)
	{
		foreach ($fee_amount as $insId => &$value) {
			foreach ($value as $key => $val) {
				$val->allocate_enter_amount = $val->component_amount - $val->component_amount_paid -  $val->concession_amount - $val->concession_amount_paid -  $val->adjustment_amount - $val->adjustment_amount_paid;
			}
		}
		return $fee_amount;
	}

	public function pay_fee()
	{
		$input = $this->input->post();
		
		//This defensive code will kick-in when a second payment is triggered when a payment is in progress.
		//Right now, closing this as it freezes payment if 
		// $check_before_insert = $this->parent_model->check_trans_status($input['fee_student_schedule_id']);
		// if ($check_before_insert) {
		// 	redirect('parent_controller/display_fee_blueprints/');
		// }
		$blue_print = $this->fees_collection_model->ge_blueprint_by_id($input['cohort_student_id']);

		$this->db->trans_begin();
		$schoolName = $this->settings->getSetting('school_short_name'); // temp testing  after remove this code
		if($schoolName =='skalvi'){
			$payment_split = $this->fees_collection_model->get_split_strategy_amount($blue_print->id, $input['split_amount']);
		}else{
			$payment_split = $this->fees_collection_model->get_split_amount($blue_print->id, $input['split_amount']);
		}
		$fTrans = $this->fees_collection_model->insert_fee_transcation($input);

		if (!$fTrans) {
			$this->session->set_flashdata('flashError', 'Transcation failed to insert');
			$this->db->trans_rollback();
			redirect('parent_controller/display_fee_blueprints/');
		} else {
			$this->db->trans_commit();
			$totalFineAmount = 0;
			$totalDiscount = 0;
			if (isset($input['total_fine_amount'])) {
				$totalFineAmount = $input['total_fine_amount'];
			}
			if (isset($input['discount_amount'])) {
				$totalDiscount = $input['discount_amount'];
			}

			foreach ($payment_split->vendors as $key => &$value) {
				if ($key == 0) {
					$value->split_amount_fixed = $value->split_amount_fixed + $totalFineAmount - $totalDiscount;
				}
			}
			
			$transPayAmount = $input['total_amount'] + $totalFineAmount - $totalDiscount;

			$payment_gateway = $this->settings->getSetting('payment_gateway');
			switch ($payment_gateway) {
				case 'payu':
					$this->payment_payu->init_fee_payment_to_school($transPayAmount, $fTrans);
					break;
				case 'jodo':
					$payment_split_jodo = $this->fees_collection_model->get_split_amount_jodo($blue_print->id, $input['split_amount']);
					$jodo_split = [];
					foreach ($payment_split_jodo as $key => $val) {
						if($val->amount != 0){
							array_push($jodo_split, $val);
						}
					}
					if($input['payment_type_choose'] =='Flex'){
						$studentData = $this->fees_collection_model->get_student_data_for_jodoby_student_id($input['student_id']);
						$this->payment_jodo->init_fee_payment_to_school_flex($transPayAmount, $fTrans, $jodo_split, $input['payment_type_choose'], $studentData);
					}
					$this->payment_jodo->init_fee_payment_to_school($transPayAmount, $fTrans, $jodo_split);
					break;
				default:
					$this->payment->init_payment_to_school($transPayAmount, 'PARENT_FEE', $fTrans, 'parent_controller/fee_trans_done', $blue_print->is_split, json_encode($payment_split), 'REDIRECT');
					break;
			}
		}
	}

	public function fee_trans_done_transport(){
		if ($_POST['response_type'] === 'IMMEDIATE') {
			$this->__handle_immediate_op_response_transport($_POST);
		} elseif ($_POST['response_type'] === 'DELAYED') {
			// Code Delete because handleing delayed response like an immediate response
			$this->__handle_immediate_op_response_transport($_POST);
		} else {
			$this->__handle_recon_op_response($_POST);
		}
	}

	public function __handle_immediate_op_response_transport($response){
		if ($response['transaction_status'] === 'SUCCESS') {

			$is_receipt_generated = $this->fees_collection_model->is_receipt_generated($response['source_id']);

			// if receipt is already generated, we have already updated the transaction and schedule table. No need to update again. Just show the already generated receipt.
			if ($is_receipt_generated) {
				redirect('parent_controller/fee_reciept/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
				return;
			}

			// generate and update receipt number after transcation
			$result =  $this->fees_collection_model->update_trans_student_all_table($response['source_id']);

			// $this->fees_collection_model->update_receipt_transcation_wise($response['source_id']);

			// $this->fees_collection_model->update_student_schedule_all_table($response['source_id']);

			if (!$result) {
				$this->session->set_flashdata('flashError', 'Something went wrong. Will get back to you shortly');
			} else {
				$this->session->set_flashdata('flashSuccess', 'Transcation successful');
			}

			redirect('parent_controller/fee_reciept/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
		} else {
			//Online payment failed
			$this->fees_collection_model->update_transcation_status($response['source_id'], 'FAILED');

			// $this->session->set_flashdata('flashError', 'Online payment failed. If your Bank account is credited, check back after sometime');
			$transId = 0;
			if(!empty($response['transaction_id'])){
				$transId = $response['transaction_id'];
			}
			
			redirect('parent_controller/transport_request');
			
		}
	}

	public function fee_trans_done()
	{
		/*** 
		 * Sample POST input
		 * Array
				(
						[transaction_status] => SUCCESS
						[source_id] => 12
						[display_message] => Transaction Successful!
						[transaction_id] => <id>
						[transaction_date] = <date>
						[response_type] = IMMEDIATE/RECON
				)
		 * 
		 * 
		 */
		// trigger_error("Response at fee_trans_done");
		// trigger_error(json_encode($_POST));

		if ($_POST['response_type'] === 'IMMEDIATE') {
			$this->__handle_immediate_op_response($_POST);
		} elseif ($_POST['response_type'] === 'DELAYED') {
			// Code Delete because handleing delayed response like an immediate response
			$this->__handle_immediate_op_response($_POST);
		} else {
			$this->__handle_recon_op_response($_POST);
		}
	}


	private function __handle_immediate_op_response($response)
	{
		if ($response['transaction_status'] === 'SUCCESS') {

			$is_receipt_generated = $this->fees_collection_model->is_receipt_generated($response['source_id']);

			// if receipt is already generated, we have already updated the transaction and schedule table. No need to update again. Just show the already generated receipt.
			if ($is_receipt_generated) {
				redirect('parent_controller/fee_reciept/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
				return;
			}

			// generate and update receipt number after transcation
			$result =  $this->fees_collection_model->update_trans_student_all_table($response['source_id']);

			// $this->fees_collection_model->update_receipt_transcation_wise($response['source_id']);

			// $this->fees_collection_model->update_student_schedule_all_table($response['source_id']);

			if (!$result) {
				$this->session->set_flashdata('flashError', 'Something went wrong. Will get back to you shortly');
			} else {
				$this->session->set_flashdata('flashSuccess', 'Transcation successful');
			}

			redirect('parent_controller/fee_reciept/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
		} else {
			//Online payment failed
			$this->fees_collection_model->update_transcation_status($response['source_id'], 'FAILED');

			// $this->session->set_flashdata('flashError', 'Online payment failed. If your Bank account is credited, check back after sometime');
			$transId = 0;
			if(!empty($response['transaction_id'])){
				$transId = $response['transaction_id'];
			}
			
			redirect('parent_controller/fee_transaction_failed/' . $response['source_id'] . '/' . $transId . '/' . $response['transaction_date'] . '/' . $response['transaction_time'] . '/' . $response['tx_response_code']);
			
		}
	}

	// Code Delete because handleing delayed response like an immediate response
	// private function __handle_delayed_op_response($response){
	// 	if ($response['transaction_status'] === 'SUCCESS') {
	// 		// generate and update receipt number after transcation
	// 		$this->fees_collection_model->update_receipt_transcation_wise($response['source_id']);

	// 		//Online payment successful
	// 		$result = $this->fees_collection_model->update_student_schedule_all_table($response['source_id']);
	// 		if (!$result) {
	// 			$this->session->set_flashdata('flashError', 'Something went wrong. Will get back to you shortly');
	// 		} else {
	// 			$this->session->set_flashdata('flashSuccess', 'Transcation successful');
	// 		}
	// 		redirect('parent_controller/fee_reciept/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
	// 	} else {
	// 		//Online payment failed
	// 		$this->fees_collection_model->update_transcation_status($response['source_id'], 'FAILED');
	// 		$this->session->set_flashdata('flashError', 'Online payment failed. If your Bank account is credited, check back after sometime');
	// 		redirect('parent_controller/display_fee_blueprints');
	// 	}
	// }

	private function __handle_recon_op_response($response)
	{

		//First check the transaction status from db (Success/Failure)
		$trans_status = $this->parent_model->transaction_statusby_source_id($response['source_id']);

		// Update after response recon status into transcation table
		$this->parent_model->opm_recon_status_update($response['source_id'], $trans_status->status, $response['transaction_status']);

		//Get the transaction status from $response (Success/Failure)
		//Handle Success after Success
		//Handle Failure after Failure

		if ($trans_status->status == $response['transaction_status']) {
			// No Action
		} else {
			//Handle Success after Failure
			if ($trans_status->status === 'SUCCESS') {
				if ($response['transaction_status'] === 'FAILED') {
					$result = $this->fees_collection_model->soft_delete_feereceipt($response['source_id']);
					if ($result) {
						$this->fees_collection_model->update_transcation_status($response['source_id'], 'FAILED');
					}
					//Update the transaction as successful. Update student schedule tables.
				}
				//Handle Failure after Success
			} else {
				if ($trans_status->status === 'FAILED') {
					if ($response['transaction_status'] === 'SUCCESS') {
						$result = $this->fees_collection_model->update_student_schedule_all_table($response['source_id']);
						if ($result) {
						}
					}
				}
			}
		}
		//Update the transaction as failed.
	}

	private function __sms_fees_payment($fee_payment_sms, $transaction_id, $amount_paid)
	{
		$stake_holder_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$studentName = $this->parent_model->getLoggedInStudentInformation($stake_holder_id);
		$sent_by = $this->authorization->getAvatarId();
		$ph_type = $this->parent_model->get_relation_type($this->authorization->getAvatarStakeHolderId());
		$sent_to_str = 'Student Individual';
		$source = $fee_payment_sms->source;
		$sh_type = $fee_payment_sms->sh_type;
		$message = $fee_payment_sms->message;
		$message = str_replace('%%student_name%%', $studentName->student_name, $message);
		$message = str_replace('%%amount%%', $amount_paid, $message);
		$message = str_replace('%%trans_id%%', $transaction_id, $message);
		$input_arr = array();
		$this->load->helper('texting_helper');
		$input_arr['student_ids'] = [$stake_holder_id];
		$input_arr['source'] = 'Fee Payment';
		$input_arr['send_to'] = 'Both';
		$input_arr['message'] = $message;
		// $text_mode_to_use = $this->settings->getSetting('text_mode_to_use');
		$input_arr['mode'] = 'sms';
		// if ($text_mode_to_use) {
		// 	$input_arr['mode'] = $text_mode_to_use;
		// }
		$response = sendText($input_arr);
		if ($response['success'] != '') {
			$status = 1;
		} else {
			$status = 0;
		}
		return $status;
	}

	public function fee_reciept($fTrans, $transaction_id, $transaction_date, $transaction_time, $traverse_to = '')
	{
		// trigger_error("Response at fee_recipt");
		// trigger_error("fTrans: $fTrans");
		// trigger_error("transaction_id $transaction_id");
		// trigger_error("transaction_date: $transaction_date");
		// trigger_error("transaction_time: $transaction_time");


		$this->fees_collection_model->create_pdf_template_for_fee_receipts($fTrans, $transaction_id, $transaction_date, $transaction_time);

		$data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);

		$fee_payment_sms = $this->settings->getSetting('fee_payment_sms');
		if (!empty($fee_payment_sms)) {
			if ($fee_payment_sms->sms_enabled == TRUE) {
				$this->__sms_fees_payment($fee_payment_sms, $transaction_id, $data['fee_trans']->amount_paid);
			}
		}
		// $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
		// $template = $this->fees_collection_model->get_fee_template_by_blueprint_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
		$blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
		if($blue_print->enable_staff_notification == 1){
			$staffIds = json_decode($blue_print->notification_staff_ids);
			if(!empty($staffIds)){
				$this->_staff_notification_parent_fees($staffIds, $blue_print->name, $fee_trans->student->stdName, $fee_trans->student->clsName, $fee_trans->amount_paid);
			}	
		}

		if($blue_print->enable_parent_notification == 1){
			$stake_holder_id = [$this->parent_model->getStudentIdOfLoggedInParent()];
			if(!empty($stake_holder_id)){
				$this->_parent_notification_paid_fees($stake_holder_id, $blue_print->name, $fee_trans->student->stdName, $fee_trans->student->clsName, $fee_trans->amount_paid, $transaction_id);
			}	
		}

		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$email_content = $this->parent_model->get_parent_email($student_id,'fees_payment_success_confirmation_email');
		;
		$emailTransArry = [
			'paid_amount'=>$data['fee_trans']->amount_paid,
			'transaction_mode' => $data['fee_trans']->transaction_mode . '(' . $data['fee_trans']->online_tx_mode . ')',
			 'paid_datetime' => $data['fee_trans']->paid_datetime
		];

		if(!empty($email_content)){

			$sent_data = [];
			// Add father's data
			$father_data = new stdClass();
			$father_data->id = $email_content['to_email']->f_id;
			$father_data->email = $email_content['to_email']->father_email;
			$father_data->avatar_type = 2;
			$sent_data[] = $father_data;
			
			// Add mother's data
			$mother_data = new stdClass();
			$mother_data->id = $email_content['to_email']->m_id;
			$mother_data->email = $email_content['to_email']->mother_email;
			$mother_data->avatar_type = 2;
			$sent_data[] = $mother_data;

			$mail_sent = $this->_fee_email_to_parent($email_content,$emailTransArry);
			if($mail_sent){
				$sender_list = [];
				$sender_list['students'] = [[
					'send_to' => 'Both',
					'send_to_type' => 'Father',
					'ids' => $student_id,
				],
				[
					'send_to' => 'Both',
					'send_to_type' => 'Mother',
					'ids' => $student_id,
				]];
				
				$email_master_data = array(
				'subject' => $mail_sent['email_subject'],
				'body' => $mail_sent['template_content'],
				'source' => 'Fee Payment Done',
				'sent_by' => $this->authorization->getAvatarId(),
				'recievers' => 'Parents',
				'from_email' => $mail_sent['registered_email'],
				'files' => null,
				'acad_year_id' => $this->acad_year->getAcadYearId(),
				'visible' => 1,
				'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
				'sending_status' => 'Completed',
				'texting_master_id'=>''
				);
				$this->load->model('communication/emails_model');
				$this->load->helper('email_helper');
				$email_master_id = $this->emails_model->saveEmail($email_master_data);
				$status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
				sendEmail($mail_sent['template_content'], $mail_sent['email_subject'], 0, $mail_sent['memberEmail'], $mail_sent['registered_email'], '');
			}
		}
		// $payment_modes = json_decode($blue_print->allowed_payment_modes);

		// $result = $this->_create_template_fee_amount($data['fee_trans'], $template, $transaction_id, $transaction_date, $transaction_time, $payment_modes);
		// $update =  $this->fees_collection_model->update_html_receipt($result, $fTrans);
		// if ($update) {
		// 	$this->testPost($result, $fTrans, $blue_print->receipt_for);
		// }
		redirect('parent_controller/fee_reciept_view/' . $fTrans . '/' . $transaction_id . '/' . $transaction_date . '/' . $transaction_time . '/' . $traverse_to);
	}

	public function fee_reciept_view($fTrans, $transaction_id, $transaction_date, $transaction_time, $traverse_to = '')
	{
		$data['traverse_to'] = $traverse_to;
		$data['transaction_id'] = $transaction_id;
		$data['transaction_date'] = $transaction_date;
		$data['transaction_time'] = $transaction_time;
		$data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
		if($data['fee_trans']->no_of_comp->is_transport_request == 1){
			redirect('parent_controller/transport_request');
		}else{
			$data['main_content'] = 'parent/feesv2/receipt_parent';
		}
		$this->load->view('inc/template', $data);
	}


	public function fee_transaction_failed($fTrans, $transaction_id, $transaction_date, $transaction_time, $response_code)
	{
		$data['transaction_id'] = $transaction_id;
		$data['transaction_date'] = $transaction_date;
		$data['transaction_time'] = $transaction_time;
		$data['tx_response_code'] = $response_code;
		// $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
		$data['main_content'] = 'parent/feesv2/receipt_failed';
		$this->load->view('inc/template', $data);
	}


	public function testPost($html, $fTrans, $receipt_for, $pdf_page_mode)
	{
		$school = CONFIG_ENV['main_folder'];
		$path = $school . '/fee_reciepts/' . uniqid() . '-' . time() . ".pdf";

		$bucket = $this->config->item('s3_bucket');

		$status = $this->fees_collection_model->updateFeePath($fTrans, $path);

		$page = $pdf_page_mode;
		$page_size = 'a4';
		if ($receipt_for === 'ourschool_academic') {
			$page_size = 'a5';
			$page = 'portrait';
		}
		if ($receipt_for === 'vinayaka') {
			$page = 'portrait';
		}

		// $page = 'landscape';
		$page = '';
		$curl = curl_init();

		$postData = urlencode($html);

		$username = CONFIG_ENV['job_server_username'];
		$password = CONFIG_ENV['job_server_password'];
		$return_url = site_url() . 'Callback_Controller/updateFeePdfLink';

		curl_setopt_array($curl, array(
			CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_USERPWD => $username . ":" . $password,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "POST",
			CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&page=" . $page . "&page_size=" . $page_size . "&data=" . $postData . "&return_url=" . $return_url,
			CURLOPT_HTTPHEADER => array(
				"Accept: application/json",
				"Cache-Control: no-cache",
				"Content-Type: application/x-www-form-urlencoded",
				"Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
			),
		));

		$response = curl_exec($curl);
		$err = curl_error($curl);
		curl_close($curl);
		// if ($err) {
		//   echo 0;
		// } else {
		//   echo 1;
		// }
	}

	public function _create_template_fee_amount($fee_trans, $template, $transaction_id, $transaction_date, $transaction_time, $parent_name, $payment_modes)
	{
		$medium = $this->settings->getSetting('medium')[$fee_trans->student->medium];
		$schoolName = $this->settings->getSetting('school_short_name');
		$class = $fee_trans->student->classSection;
		if ($fee_trans->student->is_placeholder == 1) {
			$class = $fee_trans->student->clsName;
		}
		$createdByParentName = '';
		if($fee_trans->transaction_mode =='ONLINE'){
		$createdByParentName.='<td style="padding-left: 20%;"><b>Parent:</b>'.$parent_name->parent_name.'</td>';
		$createdByParentName.='<td style="padding-left: 35%;"></td>';
		}else{
		$createdByParentName.='<td style="padding-left: 20%;"><b>Father Name:</b>'.$fee_trans->student->fName.'</td>';
		$createdByParentName.='<td style="padding-left: 35%;"><b>Mother Name:</b>'.$fee_trans->student->mName.'</td>';
		}

		$template = str_replace('%%receipt_no%%', $fee_trans->receipt_number, $template);
		$template = str_replace('%%class%%', $class, $template);
		$template = str_replace('%%class_name%%', $fee_trans->student->clsName, $template);
		$template = str_replace('%%class_medium%%', $class . '/' . $medium, $template);
		$template = str_replace('%%transaction_id%%', $transaction_id, $template);
		$template = str_replace('%%transaction_date%%', date('d-m-Y', strtotime($fee_trans->paid_datetime)), $template);
		$template = str_replace('%%f_number%%', $fee_trans->student->mobile_no, $template);
		$template = str_replace('%%student_name%%', $fee_trans->student->stdName, $template);
		$template = str_replace('%%father_name%%', $fee_trans->student->fName, $template);
		$template = str_replace('%%mother_name%%', $fee_trans->student->mName, $template);
		$template = str_replace('%%admission_no%%', $fee_trans->student->admission_no, $template);
		$template = str_replace('%%sts_number%%', $fee_trans->student->sts_number, $template);
		$template = str_replace('%%remarks%%', $fee_trans->remarks, $template);
		$template = str_replace('%%academic_year%%', $this->acad_year->getAcadYearById($fee_trans->acad_year_id), $template);
		$template = str_replace('%%enrollment_number%%', $fee_trans->student->enrollment_number, $template);
		$template = str_replace('%%roll_no%%', $fee_trans->student->roll_no, $template);
		$template = str_replace('%%combination%%', $fee_trans->student->combination, $template);
		$template = str_replace('%%f_mobile_no%%', $fee_trans->student->mobile_no, $template);
		$template = str_replace('%%semester%%', $fee_trans->student->semester, $template);
		$template = str_replace('%%createdByParentName%%', $createdByParentName, $template);
		$style = "";
		if ($schoolName == 'prarthana') {
			$style = 'text-align:right';
		}
		$con_remaarks = 'Concession (-)';
		if ($schoolName == 'divine') {
			$con_remaarks = 'Concession (-)<br> (Deductions Covid - 19)';
		}
		$i = 1;
		$i1 = 1;
		$cnt = 0;
		$cnt1 = 0;
		$sl = 0;
		$sl1 = 0;
		$t = 1;
		$totalAmount = 0;
		$concessionTotal = 0;
		$adjustmentTotal = 0;
		$totalAmountPaid = 0;
		if ($fee_trans->no_of_ins->ins_count > 1) {
			$colspan = 3;
		} else {
			$colspan = 2;
		}
		$header_part = '<table>';
		$header_part .= '<tr>';
		$header_part .= '<th width="10%">#</th>';
		if ($fee_trans->no_of_ins->ins_count > 1) {
			$header_part .= '<th width="25%">Installment</th>';
		}
		$header_part .= '<th>Particulars</th>';
		$header_part .= '<th style=' . $style . '>Amount</th>';
		$header_part .= '</tr>';
		$rowspan = $fee_trans->no_of_comp->comp_count;
		$component_part = '';
		$component_part_paid = '';
		foreach ($fee_trans->comp as $key => $trans) {
			$totalAmount += $trans->amount_paid + $trans->concession_amount + $trans->adjustment_amount;
			$totalAmountPaid += $trans->amount_paid;
			$concessionTotal += $trans->concession_amount;
			$adjustmentTotal += $trans->adjustment_amount;
			$component_part .= '<tr>';
			if (!$sl) {
				$component_part .= '<td style="vertical-align: middle;" rowspan="' . $rowspan . '">' . $i++ . '</td>';
				$sl .= $rowspan;
			}
			$sl--;

			if ($fee_trans->no_of_ins->ins_count >= 2) {
				if (!$cnt) {
					$component_part .= '<td style="vertical-align: middle;" rowspan="' . $rowspan . '" >' . $trans->insName . '</td>';
					$cnt = $rowspan;
				}
				$cnt--;
			}

			$component_part .= '<td>' . $trans->compName . '</td>';
			$component_part .= '<td style=' . $style . '>' . ($trans->amount_paid + $trans->concession_amount) . '</td>';

			$component_part .= '</tr>';

			$component_part_paid .= '<tr>';
			if (!$sl1) {
				$component_part_paid .= '<td style="vertical-align: middle;" rowspan="' . $rowspan . '">' . $i1++ . '</td>';
				$sl1 .= $rowspan;
			}
			$sl1--;

			if ($fee_trans->no_of_ins->ins_count >= 2) {
				if (!$cnt1) {
					$component_part_paid .= '<td style="vertical-align: middle;" rowspan="' . $rowspan . '" >' . $trans->insName . '</td>';
					$cnt1 = $rowspan;
				}
				$cnt1--;
			}

			$component_part_paid .= '<td>' . $trans->compName . '</td>';
			$component_part_paid .= '<td style=' . $style . '>' . $trans->amount_paid . '</td>';

			$component_part_paid .= '</tr>';

		}

		$without_comp = '<tr>';
		$without_comp .= '<td>' . $i++ . '</td>';
		$without_comp .= '<td>' . $fee_trans->no_of_comp->blueprint_name . '</td>';
		$without_comp .= '<td>' . $totalAmount . '</td>';
		$without_comp .= '</tr>';

		$footer_part = '<tr>';
		$footer_part .= '<td colspan="' . $colspan . '" style="text-align: right;">Total Fee</td>';
		$footer_part .= '<td style=' . $style . '>' . $totalAmount . '</td>';
		$footer_part .= '</tr>';

		if ($fee_trans->concession_amount != 0) {
			$footer_part .= '<tr>';
			$footer_part .= '<td colspan="' . $colspan . '" style="text-align:right;">' . $con_remaarks . '</td>';
			$footer_part .= '<td style=' . $style . '>' . $fee_trans->concession_amount . '</td>';
			$footer_part .= '</tr>';
		}

		if ($fee_trans->adjustment_amount != 0) {
			$footer_part .= '<tr>';
			$footer_part .= '<td colspan="' . $colspan . '" style="text-align:right;">Adjustment (-)</td>';
			$footer_part .= '<td style=' . $style . '  colspan="' . $cols . '">' . $fee_trans->adjustment_amount . '</td>';
			$footer_part .= '</tr>';
		}
		if ($fee_trans->discount_amount != 0) {
			$footer_part .= '<tr>';
			$footer_part .= '<td colspan="' . $colspan . '" style="text-align:right;">Discount (-)</td>';
			$footer_part .= '<td style=' . $style . '>' . $fee_trans->discount_amount . '</td>';
			$footer_part .= '</tr>';
		}

		if ($fee_trans->fine_amount != 0) {
			$footer_part .= '<tr>';
			$footer_part .= '<td colspan="' . $colspan . '" style="text-align:right;">Late Fee</td>';
			$footer_part .= '<td style=' . $style . '>' . $fee_trans->fine_amount . '</td>';
			$footer_part .= '</tr>';
		}
		if ($schoolName == 'vinayaka') {
			$footer_part .= '<tr>';
			$footer_part .= '<td colspan="' . $colspan . '" style="text-align:right;">Fine Amount</td>';
			$footer_part .= '<td style=' . $style . '>0</td>';
			$footer_part .= '</tr>';
		}
		if ($fee_trans->card_charge_amount != 0) {
			$footer_partfooter_part .= '<tr>';
			$footer_partfooter_part .= '<td colspan="' . $colspan . '" style="text-align:right;">Card Charge Amount</td>';
			$footer_partfooter_part .= '<td style=' . $style . '>' . $fee_trans->card_charge_amount . '</td>';
			$footer_partfooter_part .= '</tr>';
		}

		$footer_part .= '<tr>';
		$footer_part .= '<td colspan="' . $colspan . '" style="text-align:right;border: solid 1px #474747;"><strong>Total</strong></td>';
		$footer_part .= '<td style=' . $style . ' style="border: solid 1px #474747;">' . ($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount) . '</td>';
		$footer_part .= '</tr>';

		$footer_without_part .= '<tr>';
		$footer_without_part .= '<td colspan="' . $colspan . '" style="text-align:right;border: solid 1px #474747;"><strong>Total</strong></td>';
		$footer_without_part .= '<td style=' . $style . ' style="border: solid 1px #474747;">' . ($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount) . '</td>';
		$footer_without_part .= '</tr>';

		$footer_part .= '</table>';
		$without_comp_dynamic_part = $header_part . $without_comp . $footer_part;
		$dynamic_part = $header_part . $component_part . $footer_part;

		$dynamic_partwithout_footer = $header_part . $component_part_paid . $footer_without_part;

		$footer_yashasvi = '<table>';
		$footer_yashasvi .= '<tr>';
		$footer_yashasvi .= '<th>Balance Amount</th>';
		$footer_yashasvi .= '<th>Entered By</th>';
		$footer_yashasvi .= '</tr>';
		$footer_yashasvi .= '<tr>';
		$footer_yashasvi .= '<td>' . ($fee_trans->no_of_ins->total_fee -  $fee_trans->no_of_ins->total_fee_paid -  $fee_trans->no_of_ins->total_concession_amount_paid) . '</td>';
		$footer_yashasvi .= '<td></td>';
		$footer_yashasvi .= '</tr>';
		$footer_yashasvi .= '<tr>';
		$footer_yashasvi .= '<td></td>';
		$footer_yashasvi .= '<td>This is computer generated receipt. No signature required</td>';
		$footer_yashasvi .= '</tr>';
		$footer_yashasvi .= '</table>';

		$next_due_list = '<tr>';
		$next_due_list .= '<th>Particulars</th>';
		$next_due_list .= '<th>Amount</th>';
		if ($this->settings->getSetting('school_short_name') != 'npsjnr') {
			$next_due_list .= '<th>Due Date</th>';
		}
		$next_due_list .= '</tr>';
		$fee_balance_total = 0;
		foreach ($fee_trans->bal_ins as $bal) {
			$fee_balance_total += $bal->ins_balance;
			$next_due_list .= '<tr>';
			$next_due_list .= '<td>' . $bal->installment_name . '</td>';
			$next_due_list .= '<td>' . $bal->ins_balance . '</td>';
			if ($this->settings->getSetting('school_short_name') != 'npsjnr') {
				$next_due_list .= '<td>' . $bal->due_date . '</td>';
			}
			$next_due_list .= '</tr>';
		}

		// $fee_balance = $fee_trans->no_of_ins->total_fee -  $fee_trans->no_of_ins->total_fee_paid -  $fee_trans->no_of_ins->total_concession_amount_paid;
		$fee_balance = $fee_balance_total;
		$amountInWords = $this->getIndianCurrency($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount);
		$totalAmountString = $fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount;
		$paymentTypeString = '';
		$paymentTypeChequeNumberString = '';
		$paymentTypeChequeDateString = '';
		$paymentTypeBankDateString = '';
		$payment_mode = '<table>';
		if ($fee_trans->payment_type == '10') {
			$payment_mode .= '<tr>';
			$payment_mode .= '<td><strong>Payment Type : </strong> Online Payment</td>';
			$payment_mode .= '</tr>';
			$paymentTypeString = 'Online Payment';
		} else {
			foreach ($payment_modes as $key => $type) {
				if ($type->value == $fee_trans->payment_type) {
					$paymentTypeString = strtoupper($type->name);
					$paymentTypeChequeNumberString = $fee_trans->cheque_dd_nb_cc_dd_number;
					$paymentTypeChequeDateString = date('d-m-Y', strtotime($fee_trans->cheque_or_dd_date));
					$paymentTypeBankDateString = $fee_trans->bank_name;
					if ($type->value == '1' || $type->value == '4') {
						$payment_mode .= '<tr>';
						$payment_mode .= '<td style="border:none" >Payment Type : ' . strtoupper($type->name) . '</td>';
						$payment_mode .= '<td style="border:none">Date :' . date('d-m-Y', strtotime($fee_trans->cheque_or_dd_date)) . '</td>';
						$payment_mode .= '<td style="border:none">Drawn On :' . $fee_trans->bank_name . '</td>';
						$payment_mode .= '<td style="border:none">Number :' . $fee_trans->cheque_dd_nb_cc_dd_number . '</td>';
						$payment_mode .= '</tr>';
					} else {
						$payment_mode .= '<tr>';
						$payment_mode .= '<td>Payment Type ' . strtoupper($type->name) . '</td>';
						$payment_mode .= '</tr>';
					}
				}
			}
		}

		$payment_mode .= '</table>';

		$template = str_replace('%%installements%%', $dynamic_part, $template);
		$template = str_replace('%%paymentTypeString%%', $paymentTypeString, $template);
		$template = str_replace('%%totalAmountString%%', $totalAmountString, $template);
		$template = str_replace('%%paymentTypeChequeNumberString%%', $paymentTypeChequeNumberString, $template);
		$template = str_replace('%%paymentTypeChequeDateString%%', $paymentTypeChequeDateString, $template);
		$template = str_replace('%%paymentTypeBankDateString%%', $paymentTypeBankDateString, $template);
		$template = str_replace('%%installements_without_components%%', $without_comp_dynamic_part, $template);
		$template = str_replace('%%payment_modes%%', $payment_mode, $template);
		$template = str_replace('%%rupees_in_words%%', ucwords($amountInWords), $template);
		$template = str_replace('%%footer_yashasvi%%', $footer_yashasvi, $template);
		$template = str_replace('%%fee_balance%%', $fee_balance, $template);
		$template = str_replace('%%next_due_list%%', $next_due_list, $template);
		$template = str_replace('%%fees_total_amount%%', $totalAmount, $template);
		$template = str_replace('%%fees_paid_amount%%', $totalAmountPaid, $template);
		$template = str_replace('%%fees_concession_amount%%', $concessionTotal, $template);
		$template = str_replace('%%fees_adjustment_amount%%', $adjustmentTotal, $template);
		$template = str_replace('%%installements_without_footer%%', $dynamic_partwithout_footer, $template);

		return $template;
	}

	function getIndianCurrency(float $number)
	{
		$schoolName = $this->settings->getSetting('school_short_name');
		$decimal = round($number - ($no = floor($number)), 2) * 100;
		$hundred = null;
		$digits_length = strlen($no);
		$i = 0;
		$str = array();
		$words = array(
			0 => '', 1 => 'one', 2 => 'two',
			3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
			7 => 'seven', 8 => 'eight', 9 => 'nine',
			10 => 'ten', 11 => 'eleven', 12 => 'twelve',
			13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
			16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
			19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
			40 => 'forty', 50 => 'fifty', 60 => 'sixty',
			70 => 'seventy', 80 => 'eighty', 90 => 'ninety'
		);
		$digits = array('', 'hundred', 'thousand', 'lakh', 'crore');
		while ($i < $digits_length) {
			$divider = ($i == 2) ? 10 : 100;
			$number = floor($no % $divider);
			$no = floor($no / $divider);
			$i += $divider == 10 ? 1 : 2;
			if ($number) {
				$plural = (($counter = count($str)) && $number > 9) ? '' : null;
				$hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
				$str[] = ($number < 21) ? $words[$number] . ' ' . $digits[$counter] . $plural . ' ' . $hundred : $words[floor($number / 10) * 10] . ' ' . $words[$number % 10] . ' ' . $digits[$counter] . $plural . ' ' . $hundred;
			} else $str[] = null;
		}
		$Rupees = implode('', array_reverse($str));
		$paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
		if ($schoolName === 'prarthana') {
			return 'Rupees ' . ($Rupees ? $Rupees . 'Only ' : ' ') . $paise;
		}
		return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise;
	}

	public function receipt_pdf_download($id)
	{
		$link = $this->parent_model->download_fee_receipt($id);
		$details = $this->parent_model->download_fee_receipt_file_name($id);
		if(!empty($details)){
		  $file_name = $details->student_name . ' (' . $details->receipt_number . ') fee_reciept.pdf';
		} else {
		  $file_name = 'fee_reciept.pdf';
		}
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($file_name, $data, TRUE);
	}

	public function view_history($transvers = 0)
	{
		$data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['acad_year_selection'] = $this->parent_model->get_fee_total_acad_year($data['student_id']);

		$data['allacadyears'] = $this->acad_year->getAllYearData();
		$data['currentAcadyearId'] = $this->acad_year->getAcadYearId();
		$data['transvers'] = $transvers;
		
		
		$acadyearSelections = [];
		foreach ($data['acad_year_selection'] as $key => $year) {
			$acadyearSelections[$year->acad_year_id] = $year;
		}
		$data['currentAcadyearId'] = $this->acad_year->getAcadYearId();
		if (!array_key_exists($data['currentAcadyearId'], $acadyearSelections)) {
			foreach ($acadyearSelections as $key => $val) {
				$data['currentAcadyearId'] = $key;
			}
		}
		$data['history'] = $this->parent_model->get_fee_transaction_history($data['student_id'], $data['currentAcadyearId']);
		// echo "<pre>"; print_r($data['history']); die();
		$display_receipts = $this->settings->getSetting('display_receipts_in_parent');
		if ($display_receipts == 0) {
			$data['display_receipts'] = FALSE;
		} else {
			$data['display_receipts'] = TRUE;
		}
		$data['main_content'] = 'parent/feesv2/view_history';
		$this->load->view('inc/template', $data);
	}

	public function change_acad_year_fee_history($transvers = 0)
	{
		$data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['acad_year_selection'] = $this->parent_model->get_fee_total_acad_year($data['student_id']);

		$data['allacadyears'] = $this->acad_year->getAllYearData();
		$data['currentAcadyearId'] = $this->input->post('acadyearId');
		$data['transvers'] = $transvers;
		$data['history'] = $this->parent_model->get_fee_transaction_history($data['student_id'], $data['currentAcadyearId']);
		// echo "<pre>"; print_r($data['history']); die();
		$display_receipts = $this->settings->getSetting('display_receipts_in_parent');
		if ($display_receipts == 0) {
			$data['display_receipts'] = FALSE;
		} else {
			$data['display_receipts'] = TRUE;
		}
		$data['main_content'] = 'parent/feesv2/view_history';
		$this->load->view('inc/template', $data);
	}
	public function fees_details()
	{
		if (!$this->settings->isParentModuleEnabled('FEES')) {
			redirect('dashboard', 'refresh');
		}
		$data['feeConfig'] = $this->settings->getSetting('fees')['fee_type'];
		$data['main_content']    = 'parent/fees_details/index';
		$this->load->view('inc/template', $data);
	}

	public function fee_type_list($type)
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['std_details'] = $this->parent_model->get_student_detail($studentId);
		$data['feeConfig'] = $this->settings->getSetting('fees')['fee_type'];
		$feeConfig = $this->settings->getSetting('fees');
		$data['paymentType'] = $feeConfig['allowed_payment_modes'];
		if ($type == 1) { // Academic fees
			$filter = $this->fee_library->construct_filter($data['std_details']);
			$data['fee_component'] = $this->parent_model->get_feemasterFilter($filter);
			// $data['payment_history'] = $this->fee_transaction_concorde->getpaymenthistorbystudentwise($studentId);
			$data['payment_history'] = [];
			// $data['payment_history'] = $this->parent_model->getpaymenthistorbystudentwise($studentId);
			if ($this->mobile_detect->isMobile()) {
				$data['main_content']    = 'parent/fees_details/mobile/view_fees';
			} else {
				$data['main_content']    = 'parent/fees_details/desktop/view_fees';
			}
		} else if ($type == 2) { // Transportation fees
			$data['transport_history'] = $this->parent_model->get_transport_history($studentId);
			$data['transport_structire'] = $this->parent_model->get_transport_strucutre($studentId);
			$totalCollected = 0;
			foreach ($data['transport_history'] as $key => $value) {
				$totalCollected += $value->amount_paid;
			}
			$data['balance'] = $data['transport_structire']->amount -  $totalCollected;
			if ($this->mobile_detect->isMobile()) {
				$data['main_content']    = 'parent/fees_details/mobile/view_trans';
			} else {
				$data['main_content']    = 'parent/fees_details/desktop/view_trans';
			}
		} else if ($type == 3) { // Facilities fee
			$data['facilities'] = $this->parent_model->get_facilites_history($studentId);
			// echo "<pre>"; print_r($data['facilities']); die();
			if ($this->mobile_detect->isMobile()) {
				$data['main_content']    = 'parent/fees_details/mobile/view_facility';
			} else {
				$data['main_content']    = 'parent/fees_details/desktop/view_facility';
			}
		}
		// echo "<pre>"; 
		// print_r($data['payment_history']);
		// die();
		// print_r($data['transport_structire']);
		// print_r($data['balance']);
		$this->load->view('inc/template', $data);
	}

	public function fee_reciept_delayed($fTrans, $transaction_id, $transaction_date, $transaction_time, $traverse_to = '')
	{
		// trigger_error("Response at fee_recipt");
		// trigger_error("fTrans: $fTrans");
		// trigger_error("transaction_id $transaction_id");
		// trigger_error("transaction_date: $transaction_date");
		// trigger_error("transaction_time: $transaction_time");

		$fee_payment_sms = $this->settings->getSetting('fee_payment_sms');

		$data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
		$parent_name = $this->fees_collection_model->get_created_online_parent_name($data['fee_trans']->collected_by,$data['fee_trans']->transaction_mode);
		if (!empty($fee_payment_sms)) {
			if ($fee_payment_sms->sms_enabled == TRUE) {
				$this->__sms_fees_delayed_payment($fee_payment_sms, $transaction_id, $data['fee_trans']->amount_paid, $data['fee_trans']->student_id, $data['fee_trans']->collected_by);
			}
		}

		$template = $this->fees_collection_model->get_fee_template_by_blueprint_id();
		$result = $this->_create_template_fee_amount($data['fee_trans'], $template, $transaction_id, $transaction_date, $transaction_time, $parent_name);
		$update =  $this->fees_collection_model->update_html_receipt($result, $fTrans);
		if ($update) {
			$this->testPost($result, $fTrans);
		}
		$this->session->set_flashdata('flashSuccess', 'Payment done successful');
		redirect('payment_controller/online_transaction_report');
	}

	private function __sms_fees_delayed_payment($fee_payment_sms, $transaction_id, $amount_paid, $student_id, $transaction_by)
	{
		$stake_holder_id = [$student_id];
		$sent_by = $this->authorization->getAvatarId();
		$ph_type = $this->parent_model->get_relation_type($transaction_by);
		$sent_to_str = 'Student Individual';
		$source = $fee_payment_sms->source;
		$sh_type = $fee_payment_sms->sh_type;
		$message = $fee_payment_sms->message;
		$message = str_replace('%%amount%%', $amount_paid, $message);
		$message = str_replace('%%trans_id%%', $transaction_id, $message);
		$input_arr = array();
		$this->load->helper('texting_helper');
		$input_arr['student_ids'] = $stake_holder_id;
		$text_mode_to_use = $this->settings->getSetting('text_mode_to_use');
		$input_arr['mode'] = 'sms';
		if ($text_mode_to_use) {
			$input_arr['mode'] = $text_mode_to_use;
		}
		$input_arr['source'] = 'Fee Payment';
		$input_arr['send_to'] = $ph_type;
		$input_arr['message'] = $message;
		$response = sendText($input_arr);
		if ($response['success'] != '') {
			$status = 1;
		} else {
			$status = 0;
		}
		return $status;
	}

	// Payment History
	public function payment_history()
	{
		if (!$this->settings->isParentModuleEnabled('FEES')) {
			redirect('dashboard', 'refresh');
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['payment_history'] = $this->parent_model->getpaymenthistorbystudentwise($studentId);
		//echo "<pre>"; print_r($data['payment_history']); die();
		$data['main_content']    = 'parent/payment_history/index';
		$this->load->view('inc/template', $data);
	}

	// Parents teachers meeting
	public function ptm_meeting()
	{
		if (!$this->settings->isParentModuleEnabled('PTM')) {
			redirect('dashboard', 'refresh');
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
		$data['ptm'] = $this->parent_model->ptm_notification($data['studentData']->classId, $data['studentData']->sectionId);
		//echo "<pre>"; print_r($data['ptm']); die();
		// $data['feed_back'] = $this->parent_model->get_feedbackAll();
		$data['main_content']    = 'parent/notification/feedback/ptm';
		$this->load->view('inc/template', $data);
	}

	public function parent_feed_view($id)
	{
		if (!$this->settings->isParentModuleEnabled('PTM')) {
			redirect('dashboard', 'refresh');
		}
		$data['feedback_details'] = $this->parent_model->feedback_detailsbyptmId($id);
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
		$data['feedback_idwise'] = $this->parent_model->get_feedbackIdwise($id);
		$data['main_content']    = 'parent/notification/feedback/feedback_view';
		$this->load->view('inc/template', $data);
	}

	public function submit_feedback($id)
	{
		if (!$this->settings->isParentModuleEnabled('PTM')) {
			redirect('dashboard', 'refresh');
		}
		$feedback = $this->parent_model->parent_feedback($id);
		if ($feedback) {
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
			redirect('parent_controller/ptm_meeting');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_controller/ptm_meeting');
		}
	}

	// Attendance
	public function attendance()
	{
		if (!$this->settings->isParentModuleEnabled('ATTENDANCE')) {
			redirect('dashboard', 'refresh');
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$is_student_partially_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$deactivated_modules = json_decode($this->settings->getSetting('deactivation_modules'));

		$is_deactivated = 0;
		if(!empty($deactivated_modules)){
			if ($is_student_partially_deactivated && in_array('Attendance', $deactivated_modules)) {
				$is_deactivated = 1;
			} else {
				$is_deactivated = 0;
			}
		}
		if ($is_deactivated == 1) {
			$data["module_name"] = "Attendance";
			$data['main_content'] = 'parent/temporary_deactive_page.php';
		}else{
			$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
			$data['main_content']    = 'parent/attendance/index';
		}
		$this->load->view('inc/template', $data);
	}

	public function prepareAttendanceData($old_arr)
	{
		if (!$this->settings->isParentModuleEnabled('ATTENDANCE')) {
			redirect('dashboard', 'refresh');
		}
		$arr = [];
		$short_name = [];

		foreach ($old_arr as $key => $item) {
			$arr[$item['day']][$key] = $item;
			$short_name[$item['short_name']] = $item['short_name'];
		}

		//ksort($arr, SORT_NUMERIC);
		return ['day' => $arr, 'short_name' => $short_name];
	}

	public function student_attendance()
	{
		if (!$this->settings->isParentModuleEnabled('ATTENDANCE')) {
			redirect('dashboard', 'refresh');
		}
		$input = $this->input->post();
		$data['month'] = $input['month'];

		list($studentId, $classId, $sectionId) = explode("_", $input['studentDetails']);
		$studentData = $this->parent_model->getStudentDataById($studentId);
		$data['studentData'] = $studentData;

		$attendance = $this->parent_model->getAttendanceSessions($classId, $sectionId, $input['month'], $studentData->student_id);

		$lateComerDetails = $this->parent_model->getLateComerDetails($studentId, $input['month']);


		if (empty($attendance)) {
			$this->session->set_flashdata('flashWarning', 'No record found for the selection');
			redirect('parent_controller/attendance');
		}

		$attendance_data = $this->prepareAttendanceData($attendance);
		$data['late_coming_days'] = count($lateComerDetails);
		$data['lateComerDetails'] = $lateComerDetails;

		$present_days = 0;

		foreach ($attendance_data['day'] as $key => $day_value) {

			$day_count = 0;
			foreach ($day_value as $value) {
				if ($value['status'] == 1) {
					$day_count++;
				}
			}

			if ($day_count != 0)
				$present_days += $day_count / (count($day_value));
		}

		$data['attendance'] = $attendance_data;
		$data['present_days'] = number_format($present_days, 1);
		$data['main_content'] = 'parent/attendance/show';

		// echo "<pre>"; print_r($data); die();
		$this->load->view('inc/template', $data);
	}


	public function fetch_attendancereportmonthwise()
	{
		//TODO fetch from database after complete attendance module
		// $std_id = $this->parent_model->parentsStudentIdGet(); // common parentwise stdId get  
		// $data['attendance'] = $this->parent_model->getAttendacebyStudentwise($std_id);
		$data['main_content']    = 'parent/attendance/index';
		$this->load->view('inc/template', $data);
	}

	public function texts()
	{
		if (!$this->settings->isParentModuleEnabled('TEXTING')) {
			redirect('dashboard', 'refresh');
		}

		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$is_student_partially_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$deactivated_modules = json_decode($this->settings->getSetting('deactivation_modules'));
		$is_deactivated = 0;
		if(!empty($deactivated_modules)){
			if ($is_student_partially_deactivated && in_array('Texts', $deactivated_modules)) {
				$is_deactivated = 1;
			} else {
				$is_deactivated = 0;
			}
		}
		if ($is_deactivated == 1) {
			$data["module_name"] = "Texts";
			$data['main_content'] = 'parent/temporary_deactive_page.php';
		}else{
			$parentId = $this->authorization->getAvatarStakeHolderId();
			// $data['unread_count'] = $this->parent_model->getTextsInfo($parentId)->textCount;
			$data['main_content']    = 'parent/notification/texts/index';
		}
		$this->load->view('inc/template', $data);
	}

	public function get_textsStudentIdwise(){
		$parentId = $this->authorization->getAvatarStakeHolderId();
		$text_data = $this->parent_model->get_textsStudentIdwise($parentId,date('Y-m-d',strtotime($_POST['from_date'])),date('Y-m-d',strtotime($_POST['to_date'])));
		echo json_encode(array('text_data'=>$text_data));
	}

	public function upload_documents($callFrom = ''){
		if (!$this->settings->isParentModuleEnabled('UPLOAD_AADHAR')) {
			redirect('dashboard', 'refresh');
		}
		$data['callFrom'] = $callFrom;
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_document_data'] = $this->parent_model->get_student_documents($studentId);
		$data['document_types'] = $this->parent_model->get_document_types();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']    = 'parent/upload_document/document_upload_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/upload_document/document_upload_mobile';
		} else {
			$data['main_content']    = 'parent/upload_document/index';
		}
		$this->load->view('inc/template', $data);
	}

	public function view_text()
	{
		if (!$this->settings->isParentModuleEnabled('TEXTING')) {
			redirect('dashboard', 'refresh');
		}
		$id = $_POST['master_id'];
		$is_read = $_POST['is_read'];
		$parent_text_id = $_POST['parent_text_id'];
		// echo "<pre>"; print_r($_POST); die();
		$data['sms'] =  $this->parent_model->getTextDetail($id);
		$parent_id = $this->authorization->getAvatarStakeHolderId();
		if (!$is_read) {
			$this->parent_model->makeTextRead($parent_text_id);
		}

		if ($this->mobile_detect->isTablet()) {
			$data['main_content']    = 'parent/notification/texts/tablet_sms_view';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/notification/texts/mobile_sms_view';
		} else {
			$data['main_content']    = 'parent/notification/texts/sms_view';
		}
		$this->load->view('inc/template', $data);
	}

	public function view_old_text()
	{
		if (!$this->settings->isParentModuleEnabled('TEXTING')) {
			redirect('dashboard', 'refresh');
		}
		$id = $_POST['master_id'];
		$data['sms'] =  $this->parent_model->getSMSDetail($id);
		// echo "<pre>"; print_r($data['sms']); die();
		$data['main_content']    = 'parent/notification/texts/sms_view';
		$this->load->view('inc/template', $data);
	}

	public function non_compliance()
	{
		if (!$this->settings->isParentModuleEnabled('NON_COMPLIANCE')) {
			redirect('dashboard', 'refresh');
		}
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['non_compliance'] = $this->parent_model->getNonComplianceOfStudent($student_id);

		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'parent/non_compliance/parent_index_tablet.php';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content'] = 'parent/non_compliance/parent_index_mobile.php';
		} else {
			$data['main_content'] = 'parent/non_compliance/parent_index_desktop.php';
		}

		$this->load->view('inc/template', $data);
	}

	public function canteen_transaction_parent()
	{
		if (!$this->settings->isParentModuleEnabled('CANTEEN')) {
			redirect('dashboard', 'refresh');
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['studentwise_canteen'] =  $this->parent_model->get_canteenStudentIdwise($studentId);
		//echo "<pre>"; print_r($data['studentwise_canteen']); die();
		$data['main_content']    = 'parent/canteen/index';
		$this->load->view('inc/template', $data);
	}

	public function circular_categories()
	{
		if (!$this->settings->isParentModuleEnabled('CIRCULARS_V2')) {
			redirect('dashboard', 'refresh');
		}
		$data['parentId'] = $this->authorization->getAvatarStakeHolderId();
		$data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();

		$all =  $this->parent_model->getCountOfCirculars($data['parentId']);
		$latest =  $this->parent_model->latestCirculars($data['parentId']);

		$old_all =  $this->parent_model->getCountOfOldCirculars($data['student_id']);

		// $categories = json_decode($this->settings->getSetting('circularv2_categories'));
		$this->load->model('communication/circular_model', 'circular');
		$data['categories'] = $this->circular->getCircularCategories();
		$data['all'] = array();
		$data['latest'] = array();
		foreach ($data['categories'] as $key => $category) {
			$data['all'][$category->category_name] = 0;
			$data['latest'][$category->category_name] = 0;
			if (array_key_exists($category->category_name, $all)) {
				$data['all'][$category->category_name] += $all[$category->category_name];
			}
			if (array_key_exists($category->category_name, $latest)) {
				$data['latest'][$category->category_name] += $latest[$category->category_name];
			}
			if (array_key_exists($category->category_name, $old_all)) {
				$data['all'][$category->category_name] += $old_all[$category->category_name];
			}
		}
		// echo "<pre>"; print_r($all); die();
		$data['main_content']    = 'parent/notification/circulars_new/index';
		$this->load->view('inc/template', $data);
	}

	public function category_wise_circulars()
	{
		if (!$this->settings->isParentModuleEnabled('CIRCULARS_V2')) {
			redirect('dashboard', 'refresh');
		}
		$input = $this->input->post();
		$data['studentwise_circular'] = $this->parent_model->getCircularsAndEmails($input['category'], $input['parent_id']);
		$data['old_circular'] = $this->parent_model->getOldCirculars($input['category'], $input['student_id']);
		// echo "<pre>"; print_r($input); die();
		$data['category'] = $input['category'];
		$data['parent_id'] = $input['parent_id'];
		$data['student_id'] = $input['student_id'];
		$data['main_content']    = 'parent/notification/circulars_new/category_wise_titles';
		$this->load->view('inc/template', $data);
	}

	public function view_circular_new()
	{
		if (!$this->settings->isParentModuleEnabled('CIRCULARS_V2')) {
			redirect('dashboard', 'refresh');
		}
		$input = $this->input->post();
		$data['category'] = $input['category'];
		$data['circular_id'] = $input['circular_id'];
		$data['parent_id'] = $input['parent_id'];
		$data['student_id'] = $input['student_id'];
		$data['circularData'] = $this->parent_model->getCircularDeatils($input['circular_id']);
		// echo "<pre>"; print_r($data); die();
		$data['main_content']    = 'parent/notification/circulars_new/circular';
		$this->load->view('inc/template', $data);
	}

	public function view_circular_old()
	{
		if (!$this->settings->isParentModuleEnabled('CIRCULARS_V2')) {
			redirect('dashboard', 'refresh');
		}
		$input = $this->input->post();
		$data['category'] = $input['category'];
		$data['circular_id'] = $input['circular_id'];
		$data['parent_id'] = $input['parent_id'];
		$data['student_id'] = $input['student_id'];
		$data['circularData'] = $this->parent_model->getOldCircularDeatils($input['circular_id']);
		// echo "<pre>"; print_r($data); die();
		$data['main_content']    = 'parent/notification/circulars_new/circular';
		$this->load->view('inc/template', $data);
	}

	public function view_homework($id)
	{
		if (!$this->settings->isParentModuleEnabled('HOMEWORK')) {
			redirect('dashboard', 'refresh');
		}

		$data['homework'] = $this->parent_model->getHomeworkById($id);
		$data['submission_enabled'] = $this->settings->isParentModuleEnabled('HOMEWORK_SUBMISSION') && ($data['homework']['expect_submissions']);
		$return = $this->parent_model->insert_homwork_seendata($id);
		$data['main_content'] = 'parent/homework/view_homework';
		// echo "<pre>"; print_r($data); die();
		$this->load->view('inc/template', $data);
	}

	public function get_homework_by_id(){
		$id= $_POST['homeworkId'];
		$homework = $this->parent_model->getHomeworkById($id);
		echo json_encode($homework);
	}

	public function homework_submissions($homework_id)
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['homework'] = $this->parent_model->getHomeworkById($homework_id);
		$data['submissions'] = $this->parent_model->getHomeworkSubmissions($homework_id, $studentId);
		$instructions = $this->settings->getSetting('homework_submission_instructions');
		$data['instructions'] = array();
		if ($instructions) {
			$data['instructions'] = json_decode($instructions);
		}
		$data['main_content'] = 'parent/homework/homework_submissions';
		// echo "<pre>"; print_r($data); die();
		$this->load->view('inc/template', $data);
	}

	/* NOT USING THIS FUNCTION
	public function task_submissions($task_id){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['task'] = $this->parent_model->getSingleStudentTask($task_id,$studentId);
		if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/student_tasks/task_submission_mobile';
		}
		$this->load->view('inc/template', $data); 
	}*/

	public function deleteSubmission()
	{
		$hs_id = $_POST['hs_id'];
		echo $this->parent_model->deleteSubmission($hs_id);
	}

	public function submit_homework()
	{
		$homework_id = $this->input->post('homework_id');
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$file = $this->s3FileUpload($_FILES['submission_file']);
		$status = $this->parent_model->submit_homework($studentId, $file['file_name']);
		if ($status) {
			//send notification to staff
			$homework = $this->parent_model->getMinimalHomeworkById($homework_id);
			$student = $this->parent_model->getStudentDataById($studentId);
			$message = 'Homework of ' . $homework->homework_date . ' is submitted by ' . $student->stdName . ' of class ' . $student->className . '' . $student->sectionName;
			$this->load->helper('texting_helper');
			$input_arr = array();
			$input_arr['staff_ids'] = [$homework->staff_id];
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Homework';
			$input_arr['message'] = $message;
			$input_arr['staff_url'] = site_url('homework/view_submissions/') . $homework_id;
			sendText($input_arr);
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
		}
		redirect('parent_controller/homework_submissions/' . $homework_id);
	}
	public function submit_task_acknowledge()
	{
		$task_id = $this->input->post('task_id');
		$task_student_id = $this->input->post('task_student_id');
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();

		$status = $this->parent_model->acknowledge_submission($task_student_id, $task_id);

		/*if(isset($_FILES['submission_file'])){
			$file = $this->s3FileUpload($_FILES['submission_file']);
			$status = $this->parent_model->acknowledge_submission($task_student_id, $file['file_name'],$task_id);
		}
		else{
			$status = $this->parent_model->acknowledge_submission($task_student_id, '',$task_id);
		}*/
		if ($status) {
			//send notification to staff
			// $task = $this->parent_model->getSingleStudentTask($task_id,$studentId);
			$task = $this->parent_model->getTaskMinimumInfo($task_id);
			// $homework = $this->parent_model->getMinimalHomeworkById($homework_id);
			$student = $this->parent_model->getStudentDataById($studentId);
			$message = 'Task of ' . $task->created_on . ' is submitted by ' . $student->stdName . ' of class ' . $student->className . '' . $student->sectionName;
			$this->load->helper('texting_helper');
			$input_arr = array();
			$input_arr['staff_ids'] = [$task->created_by];
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Tasks';
			$input_arr['message'] = $message;
			// $input_arr['staff_url'] = site_url('homework/view_submissions/').$homework_id;
			sendText($input_arr);
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
		}
		echo json_encode($status);
	}

	/*	NOT USING THIS FUNCTION
	public function submit_task(){
		$task_id = $this->input->post('task_id');
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		
		if(isset($_FILES['submission_file'])){
			$file = $this->s3FileUpload($_FILES['submission_file']);
			$status = $this->parent_model->submit_task_mobile($studentId, $file['file_name'],$task_id);
		}
		else{
			$status = $this->parent_model->submit_task_mobile($studentId, '',$task_id);
		}
		if ($status) {
			//send notification to staff
			$task = $this->parent_model->getSingleStudentTask($task_id,$studentId);
			// $homework = $this->parent_model->getMinimalHomeworkById($homework_id);
			$student = $this->parent_model->getStudentDataById($studentId);
			$message = 'Task of '.$task[0]->created_on.' is submitted by '.$student->stdName.' of class '.$student->className.''.$student->sectionName;
		    $this->load->helper('texting_helper');
			$input_arr = array();
			$input_arr['staff_ids'] = [$task[0]->created_by];
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Tasks';
			$input_arr['message'] = $message;
			// $input_arr['staff_url'] = site_url('homework/view_submissions/').$homework_id;
			sendText($input_arr);
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
		}
				redirect('parent_controller/view_student_task/'.$task_id);



	}*/

	public function __s3FileUpload($file)
	{
		if ($file['tmp_name'] == '' || $file['name'] == '') {
			return ['status' => 'empty', 'file_name' => ''];
		}
		return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'task_submissions');
	}

	private function get_chunk_upload_secret()
	{
		$this->config->load('s3');
		$bucket = $this->config->item('s3_bucket');
		$accessKeyId = $this->config->item('access_key');
		$this->load->library('aws_library');

		//signature v4
		$general = $this->aws_library->getPolicyAndSignature('*');
		$pdf = $this->aws_library->getPolicyAndSignature('application/pdf');
		return array(
			'access' => $accessKeyId,
			'signature' => $general['signature'],
			'pdf_signature' => $pdf['signature'],
			'policy' => $general['policy'],
			'pdf_policy' => $pdf['policy'],
			'bucket' => $bucket,
			'subdomain' => CONFIG_ENV['main_folder'],
			'short_date' => $general['short_date'],
			'iso_date' => $general['iso_date'],
			'pdf_short_date' => $pdf['short_date'],
			'pdf_iso_date' => $pdf['iso_date'],
			'region' => $general['region']
		);

		//signature v2
		$general = $this->aws_library->getPolicyAndSignatureV2('*');
		$pdf = $this->aws_library->getPolicyAndSignatureV2('application/pdf');
		return array(
			'access' => $accessKeyId,
			'signature' => $general['signature'],
			'pdf_signature' => $pdf['signature'],
			'policy' => $general['policy'],
			'pdf_policy' => $pdf['policy'],
			'bucket' => $bucket,
			'subdomain' => CONFIG_ENV['main_folder']
		);

		// prepare policy
		$policy = base64_encode(json_encode(array(
			// ISO 8601 - date('c'); generates uncompatible date, so better do it manually
			'expiration' => date('Y-m-d\TH:i:s.000\Z', strtotime('+1 day')),
			'conditions' => array(
				array('bucket' => $bucket),
				array('acl' => 'public-read'),
				array('starts-with', '$key', ''),
				// for demo purposes we are accepting only images
				// array('starts-with', '$Content-Type', 'application/pdf, *'),
				array('starts-with', '$Content-Type', '*'),
				// Plupload internally adds name field, so we need to mention it here
				array('starts-with', '$name', ''),
				array('success_action_status' => '201'),
				array('starts-with', '$chunk', ''),
				array('starts-with', '$chunks', ''),
				array('starts-with', '$Filename', ''),
			)
		)));

		//prepare policy for pdf
		$pdf_policy = base64_encode(json_encode(array(
			// ISO 8601 - date('c'); generates uncompatible date, so better do it manually
			'expiration' => date('Y-m-d\TH:i:s.000\Z', strtotime('+1 day')),
			'conditions' => array(
				array('bucket' => $bucket),
				array('acl' => 'public-read'),
				array('starts-with', '$key', ''),
				// for demo purposes we are accepting only images
				array('starts-with', '$Content-Type', 'application/pdf'),
				// Plupload internally adds name field, so we need to mention it here
				array('starts-with', '$name', ''),
				array('success_action_status' => '201'),
				array('starts-with', '$chunk', ''),
				array('starts-with', '$chunks', ''),
				array('starts-with', '$Filename', ''),
			)
		)));

		// sign policy
		$signature = base64_encode(hash_hmac('sha1', $policy, $secret, true));
		$pdf_signature = base64_encode(hash_hmac('sha1', $pdf_policy, $secret, true));
		return array('access' => $accessKeyId, 'signature' => $signature, 'pdf_signature' => $pdf_signature, 'policy' => $policy, 'pdf_policy' => $pdf_policy, 'bucket' => $bucket, 'subdomain' => CONFIG_ENV['main_folder']);
	}

	public function save_task_files()
	{
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$task_id = $_POST['task_id'];
		$student_task_id = $_POST['task_student_id'];
		$paths = $_POST['paths'];
		$submission_comment =$_POST['submission_comments'];
		

		//got task_student_id from post data
		// $student_task_id = $this->db->select('lp.id as lp_task_student_id')
		// 	->from('lp_tasks_students lp')
		// 	->where('lp.student_id', $student_id)
		// 	->where('lp.lp_tasks_id', $task_id)
		// 	->get()->row()->lp_task_student_id;

		foreach ($paths as $k => $path) {
			$files_data[] = array(
				'lp_tasks_students_id' => $student_task_id,
				'file_path' => $path['path'],
				'file_order' => $k + 1,
				'type' => 'Submission',
				'file_name' => $path['name']
			);
		}

		$status = $this->parent_model->submit_task($student_task_id, $files_data, $task_id,$submission_comment);

		$return = 0;
		if ($status) {
			//send notification to staff
			$task = $this->parent_model->getTaskMinimumInfo($task_id);
			// $task = $this->parent_model->getSingleStudentTask($task_id,$student_id);
			$student = $this->parent_model->getStudentDataById($student_id);
			$message = 'Task of ' . $task->created_on . ' is submitted by ' . $student->stdName . ' of class ' . $student->className . '' . $student->sectionName;
			$this->load->helper('texting_helper');
			$input_arr = array();
			$input_arr['staff_ids'] = [$task->created_by];
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Tasks';
			$input_arr['message'] = $message;
			sendText($input_arr);
			$return = 1;
			// $this->session->set_flashdata('flashSuccess', 'Successfully submited');
		}
		echo json_encode($return);
	}

	/*NOT USING THIS FUNCTION
	public function submit_mobile_task(){
		// echo "<pre>"; print_r($_FILES); 
		// echo "<pre>"; print_r($_POST); die();
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$task_id = $_POST['task_id'];
		$files = $_FILES['files'];
		$student_task_id = $this->db->select('lp.id as lp_task_student_id')
		->from('lp_tasks_students lp')
		->where('lp.student_id', $student_id)
		->where('lp.lp_tasks_id', $task_id)
		->get()->row()->lp_task_student_id;

		foreach ($files['name'] as $k => $file_name) {
			$temp = array(
				'name' => $file_name,
				'type' => $files['type'][$k],
				'tmp_name' => $files['tmp_name'][$k],
				'error' => $files['error'][$k],
				'size' => $files['size'][$k],
			);

			$file = $this->s3FileUpload($temp, 'task');
			// echo '<pre>'; print_r($file); die();
			if($file['file_name'] != '') {
				$files_data[] = array(
					'lp_tasks_students_id' => $student_task_id,
					'file_path' => $file['file_name'],
					'file_order' => $k+1,
					'type' => 'Submission',
					'file_name' => $file_name
				);
			}
		}
		// echo '<pre>'; print_r($files_data); die();

		$status = $this->parent_model->submit_task($student_task_id, $files_data, $task_id);

		$return = 0;
		if ($status) {
			//send notification to staff
			$task = $this->parent_model->getSingleStudentTask($task_id,$student_id);
			$student = $this->parent_model->getStudentDataById($student_id);
			$message = 'Task of '.$task[0]->created_on.' is submitted by '.$student->stdName.' of class '.$student->className.''.$student->sectionName;
		    $this->load->helper('texting_helper');
			$input_arr = array();
			$input_arr['staff_ids'] = [$task[0]->created_by];
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Tasks';
			$input_arr['message'] = $message;
			sendText($input_arr);
			$return = 1;
			// $this->session->set_flashdata('flashSuccess', 'Successfully submited');
		}
		echo json_encode($return);
	}*/

	/* NOT USING THIS FUNCTION
	public function submit_task_desktop(){
    
		
		$location = $_POST['location'];
		// echo "Task<pre>";print_r($_POST);die();

		$task_id = $this->input->post('task_id');
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();

		$lp_task_id = $this->db->select('lp.id as lp_task_student_id')
		->from('lp_tasks_students lp')
		->where('lp.student_id', $studentId)
		->where('lp.lp_tasks_id', $task_id)
		->get()->row();
		$student_task_id = $lp_task_id->lp_task_student_id;
		$prefix = $this->filemanager->getFilePath('');
		$fileNames = explode(",", $this->input->post('fileName'));
		$locations = explode(",", $this->input->post('location'));
		foreach ($locations as $key => $location) {
			$files_data[] = array(
				'lp_tasks_students_id' => $student_task_id,
				'file_path' => str_replace($prefix, "", $location),
				'file_order' => $key+1,
				'type' => 'Submission',
				'file_name' => $fileNames[$key]
			);
		}

		$status = $this->parent_model->submit_task($student_task_id, $files_data, $task_id);

		if ($status) {
			//send notification to staff
			$task = $this->parent_model->getSingleStudentTask($task_id,$studentId);
			// $homework = $this->parent_model->getMinimalHomeworkById($homework_id);
			$student = $this->parent_model->getStudentDataById($studentId);
			$message = 'Task of '.$task[0]->created_on.' is submitted by '.$student->stdName.' of class '.$student->className.''.$student->sectionName;
		    $this->load->helper('texting_helper');
			$input_arr = array();
			$input_arr['staff_ids'] = [$task[0]->created_by];
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Tasks';
			$input_arr['message'] = $message;
			// $input_arr['staff_url'] = site_url('homework/view_submissions/').$homework_id;
			sendText($input_arr);
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
		}
		echo json_encode($status);
		// redirect('parent_controller/view_student_task/'.$task_id);
	}*/

	public function showAddedFiles()
	{
		$task_id = $_POST['task_id'];
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['taskName'] = $this->parent_model->getTaskName($task_id);

		$data['fileDetails'] = $this->parent_model->getAddedFileDetails($task_id, $studentId);
		echo json_encode($data);
	}

	public function deleteFile()
	{
		$file_id = $_POST['file_id'];

		$data = $this->parent_model->deleteFile($file_id);
		echo ($data);
	}

	public function getTaskName()
	{
		$task_id = $_POST['task_id'];
		$data['taskName'] = $this->parent_model->getTaskName($task_id);
		echo json_encode($data);
	}

	//Manjukiran 22-07: Changing the name of this function for IPAD to show the correct UI to download.
	//Earlier name was downloadTasksAttachment
	public function downloadMobileCircularAttachment($id, $index)
	{
		$file_link = $this->parent_model->downloadTaskAttachment($id);

		$link = $file_link->resource_file;
		$file = explode("/", $link);
		$file_name = 'task' . ($index + 1);
		$fname = $file_name . '.' . explode(".", $file[count($file) - 1])[1];
		// echo '<pre>'; print_r($fname); die();
		$signed_resource = $this->filemanager->getSignedUrlWithExpiry($file_link->resource_file, '+5 minutes');
		// $url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($signed_resource);
		$this->load->helper('download');
		force_download($fname, $data, TRUE);
		$this->load->library('user_agent');
		redirect($this->agent->referrer());
	}

	public function downloadSubmissionAttachment($id, $index)
	{
		$file_link = $this->parent_model->downloadSubmissionAttachment($id);
		$fileStudentName = $this->parent_model->getFileStudentName($id);
		$student_name = $fileStudentName->first_name;
		$fileName = $fileStudentName->file_name;

		$link = $file_link->file_path;
		$file = explode("/", $link);
		// $file_name = 'submission'.($index+1);
		$file_name1 = $student_name . '_' . $fileName;
		$ext = explode(".", $file[count($file) - 1])[1];
		$ext = ($ext == '') ? 'png' : $ext;
		$fname = $file_name1 . '.' . $ext;
		// $fname = $file_name1 .'.'.explode(".", $file[count($file)-1])[1];
		// echo '<pre>'; print_r($fname); die();
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($fname, $data, TRUE);
		$this->load->library('user_agent');
		redirect($this->agent->referrer());
	}

	/*NOT USING THIS FUNCTION
	public function getEvaluationStatus(){
		$task_id = $_POST['task_id'];
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['task'] = $this->parent_model->getSingleStudentTask($task_id, $student_id);
		// echo "<pre>";print_r($data);die();
		echo json_encode($data);
	}
*/
	private function _getFileExtension($url)
	{
		$file_path = $this->filemanager->getFilePath($url);
		$headers = get_headers($url, TRUE);
		$type = $headers['Content-Type'];
		$mime_types = MIME_TYPE_EXTENSION;
		if (array_key_exists($type, $mime_types)) {
			return $mime_types[$type];
		}
		return '';
	}

	public function downloadEvaluationAttachment($id, $index = 0)
	{
		$file_link = $this->parent_model->downloadSubmissionAttachment($id);
		$fileStudentName = $this->parent_model->getFileStudentName($id);
		$student_name = $fileStudentName->first_name;
		$fileName = $fileStudentName->file_name;
		$link = $file_link->file_path;
		$file = explode("/", $link);
		$file_name = $student_name . '_' . $fileName;

		$url = $this->filemanager->getSignedUrlWithExpiry($link,'+5 minutes');
		$ext = explode(".", $file[count($file) - 1])[1];
		if ($ext == '') {
			$ext = $this->_getFileExtension($url);
		}
		// $ext = ($ext == '')?'png':$ext;
		$fname = $file_name . '.' . $ext;
		// echo '<pre>'; print_r($fname); die();
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($fname, $data, TRUE);
		$this->load->library('user_agent');
		redirect($this->agent->referrer());
	}
	public function downloadHomeworkAttachment($id, $index)
	{
		$file_link = $this->parent_model->downloadHomeworkAttachment($id);
		if ($file_link->is_image_json) {
			$paths = json_decode($file_link->image);
			$link = $paths[$index]->path;
			$fname = $paths[$index]->name;
		} else {
			$link = $file_link->image;
			$file = explode("/", $link);
			$file_name = 'homework' . ($index + 1);
			$fname = $file_name . '.' . explode(".", $file[count($file) - 1])[1];
		}
		// echo '<pre>'; print_r($fname); die();
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($fname, $data, TRUE);
		$this->load->library('user_agent');
		redirect($this->agent->referrer());
	}

	public function downloadHomework($id)
	{
		$link = $this->parent_model->getHomeWorkImage($id);
		$url = $this->filemanager->getFilePath($link);
		$file = explode("/", $url);
		$ext = explode(".", $file[count($file) - 1])[1];
		// echo "<pre>"; print_r($ext); die();
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download('homework.' . $ext, $data, TRUE);
	}

	public function downloadHWSubmission($id)
	{
		$sub = $this->parent_model->getSubmissionData($id);
		$url = $this->filemanager->getFilePath($sub->file);
		$file = explode("/", $url);
		$ext = explode(".", $file[count($file) - 1])[1];
		// echo "<pre>"; print_r($ext); die();
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($sub->name . '.' . $ext, $data, TRUE);
	}

	public function publication()
	{
		//NOT USED
		// $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		// $data['studentInfo'] = $this->parent_model->getStudentDataById($studentId);
		// $data['publications'] = $this->parent_model->getAllPublications($studentId, $data['studentInfo']->sectionId);
		// $data['main_content']  = 'parent/notification/publication/index';
		// $this->load->view('inc/template', $data);
	}

	// public function view_tt()
	// {
	// 	if (!$this->settings->isParentModuleEnabled('TIMETABLE')) {
	// 		redirect('dashboard', 'refresh');
	// 	}
	// 	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	// 	$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
	// 	//$studentData->className
	// 	// echo '<pre>';print_r($data['studentData']);die();

	// 	if ($this->mobile_detect->isMobile()) {
	// 		$data['main_content']    = 'parent/time_table/index_mobile';
	// 	} else {
	// 		$data['main_content']    = 'parent/time_table/index_desktop';
	// 	}

	// 	$this->load->view('inc/template', $data);
	// }

	public function view_tt_v2()
	{
		if (!$this->settings->isParentModuleEnabled('TIMETABLE')) {
			redirect('dashboard', 'refresh');
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$modules = json_decode($this->settings->getSetting('deactivation_modules'));
		$data['is_deactivated'] = 0;
		if(!empty($modules)){
			if ($is_deactivated && in_array('Time Table', $modules)) {
				$data['is_deactivated'] = 1;
			} else {
				$data['is_deactivated'] = 0;
			}
		}
		
		//$studentData->className
		// echo '<pre>';print_r($data['studentData']);die();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']    = 'parent/time_table_v2/index_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/time_table_v2/index_mobile';
		} else {
			$data['main_content']    = 'parent/time_table_v2/index_desktop';
		}



		// if ($this->mobile_detect->isMobile()) {
		// 	$data['main_content']    = 'parent/time_table_v2/index_mobile';
		// } else {
		// 	$data['main_content']    = 'parent/time_table_v2/index_desktop';
		// }

		$this->load->view('inc/template', $data);
	}

	public function showAssessments()
	{
		if (!$this->settings->isParentModuleEnabled('ASSESSMENTS')) {
			redirect('dashboard', 'refresh');
		}

		$stdData = $this->parent_model->getClassIdOfLoggedInParent();
		$assIds = $this->parent_model->getAssessmentIds($stdData->classId, $stdData->sectionId);
		$data['assessments'] = array();
		foreach ($assIds as $key => $value) {
			$data['assessments'][$value->long_name] = $this->parent_model->getOverallTT($value->id);
		}
	}

	public function view_assessments()
	{
		if (!$this->settings->isParentModuleEnabled('ASSESSMENTS')) {
			redirect('dashboard', 'refresh');
		}

		$stdData = $this->parent_model->getClassIdOfLoggedInParent();
		$assIds = $this->parent_model->getAssessmentIds($stdData->classId, $stdData->sectionId);
		$data['groups'] = $this->parent_model->getGroupNames($stdData->classId);
		$data['entities'] = $this->parent_model->getEntityNames($stdData->classId);

		$data['assessments'] = array();
		$minDates = array();
		foreach ($assIds as $key => $value) {
			$arr = $this->parent_model->getOverallTT($value->id);
			$arr['assId'] = $value->id;
			$arr['sname'] = $value->short_name;
			$arr['lname'] = $value->long_name;
			$data['assessments'][] = $arr;
			$minDates[$key] = '0000-00-00';
			if (!empty($arr['min']))
				$minDates[$key] = $arr['min']->date;
		}
		array_multisort($minDates, SORT_ASC, $data['assessments']);
		// echo "<pre>"; print_r($data['assessments']);
		// print_r($minDates); die();
		$data['main_content'] = 'parent/assessments/assessment_index';
		$this->load->view('inc/template', $data);
	}

	public function showAssTimetable($assId)
	{
		if (!$this->settings->isParentModuleEnabled('ASSESSMENTS')) {
			redirect('dashboard', 'refresh');
		}

		$stdData = $this->parent_model->getClassIdOfLoggedInParent();
		$data['groups'] = $this->parent_model->getGroupNames($stdData->classId);
		$data['entities'] = $this->parent_model->getEntityNames($stdData->classId);
		$data['assData'] = $this->parent_model->getAssessmentName($assId);
		$data['assessments'] = array();
		$data['assessments'] = $this->parent_model->getAssessmentTT($assId);
		$dates = array();
		foreach ($data['assessments'] as $key => $value) {
			$value->aDates = $value->date;
			$dates[$key] = $value->date;
			if ($value->portions_at == 0) {
				$value->aDates = $value->gDate;
				$dates[$key] = $value->gDate;
			}
		}
		array_multisort($dates, SORT_ASC, $data['assessments']);
		// echo "<pre>"; print_r($data['assessments']); die();
		$data['main_content'] = 'parent/assessments/index';
		$this->load->view('inc/template', $data);
	}

	public function getTTForClassSection()
	{
		if (!$this->settings->isParentModuleEnabled('TIMETABLE')) {
			redirect('dashboard', 'refresh');
		}

		$csId = $_POST['csId'];
		$data['sectionTT'] = $this->parent_model->getTTWithStaff($csId);
		$data['headerTT'] = $this->parent_model->getPeriodHeader($csId);
		$data['wdCount'] = $this->parent_model->getNumberOfWeekDays($csId);
		$data = json_encode($data);

		//Removing Timetable from caching
		// $data = $this->session->userdata('timetable');

		// if (empty($data)) {
		// 	$data['sectionTT'] = $this->parent_model->getTT($csId);
		// 	$data['headerTT'] = $this->parent_model->getPeriodHeader($csId);
		// 	$data['wdCount'] = $this->parent_model->getNumberOfWeekDays($csId);
		// 	$data = json_encode($data);
		// 	$this->session->set_userdata('timetable', $data);
		// }

		echo $data;
	}

	public function get_day_section_timtable_v2()
	{

		$csId = $_POST['csId'];

		$data['template_obj'] = $this->parent_model->get_template_by_id($csId);
		if (!empty($data['template_obj'])) {
			$data['oc_link'] = $this->parent_model->get_section_oc_links($csId);
			$data['tt'] = $this->parent_model->get_day_section_timtable_v2($csId);
		}

		echo json_encode($data);
	}

	public function getTTV2ForClassSection()
	{
		if (!$this->settings->isParentModuleEnabled('TIMETABLE')) {
			redirect('dashboard', 'refresh');
		}

		$csId = $_POST['csId'];
		if (empty($csId)) {
			$template_id = -1;
		} else {
			$template_id = $this->template_model->get_active_template_for_section($csId);
		}

		if ($template_id == '-1') {
			$data['template_exists'] = '0';
		} else {
			$data['template_exists'] = '1';
			$ret_data = $this->template_model->get_section_temlate_for_parent_display($template_id, $csId);
			$data['sectionTT'] = $ret_data['processed_tt'];
			$data['monday'] = $ret_data['monday'];
			$data['template_obj'] = $this->template_model->get_template_by_id($template_id);
			if ($data['template_obj']->show_section_links_instead == '1') {
				$data['section_oc_links'] = $this->template_model->get_section_oc_links($csId);
			}
		}

		echo json_encode($data);
	}

	public function school_calendar()
	{
		if (!$this->settings->isParentModuleEnabled('SCHOOL_CALENDAR')) {
			redirect('dashboard', 'refresh');
		}
		
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$is_student_partially_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$deactivated_modules = json_decode($this->settings->getSetting('deactivation_modules'));
		
		$is_deactivated = 0;
		if(!empty($deactivated_modules)){
			if($is_student_partially_deactivated && in_array('Calendar', $deactivated_modules)) {
				$is_deactivated = 1;
			} else {
				$is_deactivated = 0;
			}
		}
		

		if($is_deactivated==1){
			$data["module_name"]="Calendar";
			$data['main_content'] = 'parent/temporary_deactive_page.php';
		}else{
			$data['student_board'] = $this->parent_model->getStudentBoard($studentId);
			if ($this->mobile_detect->isTablet()) {
				$data['date'] = date('Y-m');
				$data['main_content'] = 'calenderEvents/tablet_parent_calendar_events';
			} else if ($this->mobile_detect->isMobile()) {
				$data['date'] = date('Y-m');
				$data['main_content'] = 'calenderEvents/parent_calendar_events';
			} else {
				$data['year'] = date('Y');
				$data['month'] = date('Y-m');
				$data['main_content']    = 'calenderEvents/parent_calendar';
			}
		}
		// if ($this->mobile_detect->isMobile()) {
		// 	$data['date'] = date('Y-m');
		// 	$data['main_content'] = 'calenderEvents/parent_calendar_events';
		// } else {
		// 	$data['year'] = date('Y');
		// 	$data['month'] = date('Y-m');
		// 	$data['main_content']    = 'calenderEvents/parent_calendar';
		// 	// $data['main_content']    = 'calenderEvents/new2';
		// }
		$this->load->view('inc/template', $data);
	}

	public function getEvents()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$date = $_POST['date'];
		$state = $_POST['state'];
		$student_board = $_POST['student_board'];
		if ($state == 'next') {
			$date = date('Y-m', strtotime('+1 month', strtotime($date)));
		} else if ($state == 'prev') {
			$date = date('Y-m', strtotime('-1 month', strtotime($date)));
		}
		$parent = 2;
		$displayDate = date('M Y', strtotime($date));
		$eventData = $this->calenderevents_model->get_month_events_for_parents($date, $parent, $student_board, $studentId);
		$arr = array();
		foreach ($eventData as $event) {
			$key = $event->fDate;
			$eventOn = $event->fDate . ' (' . $event->fDay . ')';
			if ($event->tDate != '') {
				$key .= '-' . $event->tDate;
				$eventOn .= ' to ' . $event->tDate . ' (' . $event->tDay . ')';
			}
			if (!array_key_exists($key, $arr)) {
				$arr[$key]['event_type'] = $event->event_type;
				$arr[$key]['event_on'] = $eventOn;
				$arr[$key]['names'] = array();
				$arr[$key]['board'] = array();
			}
			array_push($arr[$key]['names'], $event->event_name);
			$arr[$key]['board'][$event->event_name] = $event->board;
		}
		$events = array();
		foreach ($arr as $value) {
			array_push($events, $value);
		}
		echo json_encode(array('events' => $events, 'displayDate' => $displayDate, 'date' => $date));
	}

	// Parent Initiative
	public function parent_initiative()
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_INITIATIVE')) {
			redirect('dashboard', 'refresh');
		}
		$data['initiative_details'] = $this->parent_model->initiative_detailsALl();
		$data['main_content']    = 'parent/notification/parent_initiative/index';
		$this->load->view('inc/template', $data);
	}

	public function parent_initiative_add()
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_INITIATIVE')) {
			redirect('dashboard', 'refresh');
		}
		$data['main_content']    = 'parent/notification/parent_initiative/add';
		$this->load->view('inc/template', $data);
	}
	public function sumbmit_parent_initiative()
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_INITIATIVE')) {
			redirect('dashboard', 'refresh');
		}
		$result = $this->parent_model->insert_parent_initative_details();
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Iniative added successfully. Please click on the arrow icon to submit to the school.');
			redirect('parent_controller/parent_initiative');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_controller/parent_initiative_add');
		}
	}
	public function parent_initiative_edit($id)
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_INITIATIVE')) {
			redirect('dashboard', 'refresh');
		}
		$data['edit_initiative'] = $this->parent_model->edit_parent_initiativebyId($id);
		$data['main_content']    = 'parent/notification/parent_initiative/edit';
		$this->load->view('inc/template', $data);
	}

	public function update_parent_initiative($id)
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_INITIATIVE')) {
			redirect('dashboard', 'refresh');
		}
		$result = $this->parent_model->update_parent_initative_details($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
			redirect('parent_controller/parent_initiative');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_controller/parent_initiative_add');
		}
	}

	public function parent_initiative_delete($id)
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_INITIATIVE')) {
			redirect('dashboard', 'refresh');
		}
		$delete = $this->parent_model->delete_parent_initativebyId($id);
		if ($delete) {
			$this->session->set_flashdata('flashSuccess', 'Successfully deleted');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
		}
		redirect('parent_controller/parent_initiative');
	}

	public function submit_parent_initiative_school($id)
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_INITIATIVE')) {
			redirect('dashboard', 'refresh');
		}
		$result = $this->parent_model->submit_parent_initiative_school($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
			redirect('parent_controller/parent_initiative');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_controller/parent_initiative');
		}
	}

	public function approved_parent_initiative_view($id)
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_INITIATIVE')) {
			redirect('dashboard', 'refresh');
		}
		$data['initiative_details'] = $this->ptm_model->initiative_submitteddetailsALl($id);
		$studentId = $this->ptm_model->get_initivatedatabyAvatarParendId($data['initiative_details']->created_by);
		$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
		$data['fatherData'] = $this->parent_model->getFatherDetails($studentId);
		$data['motherData'] = $this->parent_model->getMotherDetails($studentId);
		$data['main_content']    = 'parent/notification/parent_initiative/principal_report_view';
		$this->load->view('inc/template', $data);
	}

	public function view_marksCards()
	{
		if (!$this->settings->isParentModuleEnabled('MARKS_CARD')) {
			redirect('dashboard', 'refresh');
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$modules = json_decode($this->settings->getSetting('deactivation_modules'));
		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		// echo "<pre>"; print_r($is_deactivated); die();

		$data['is_deactivated'] = 0;
		if(!empty($modules)){
			if ($is_deactivated && in_array('Report Cards', $modules)) {
				$data['is_deactivated'] = 1;
			}
		}
		
		$data['marksCards'] = $this->parent_model->getMarksCards($studentId);
		// echo "<pre>"; print_r($data['marksCards']); die();
		$data['main_content'] = 'parent/marks_cards/index';
		$this->load->view('inc/template', $data);
	}

	public function marksCardInfo($cardId)
	{
		$data['cardDetails'] = $this->parent_model->getCardDetails($cardId);
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'parent/marks_cards/markscard_more_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/marks_cards/markscard_more';
		}
		// $data['main_content'] = 'parent/marks_cards/markscard_more';
		$this->load->view('inc/template', $data);
	}

	public function marksAnalysis($cardId)
	{
		$data['cardDetails'] = $this->parent_model->getCardDetails($cardId);
		$cardData = $this->parent_model->getCardTemplateData($data['cardDetails']->tempId);
		$assessments = json_decode($cardData->assessments);
		$assIds = array();
		foreach ($assessments as $key => $value) {
			array_push($assIds, $value->id);
		}
		$data['assessments'] = $assIds;
		$data['subjects'] = $this->parent_model->getSubjectsUnion($assIds);
		$data['main_content'] = 'parent/marks_cards/analysis_report';
		$this->load->view('inc/template', $data);
		// echo "<pre>"; print_r($data['subjects']); die();
	}

	public function assessment_portions()
	{
		if (!$this->settings->isParentModuleEnabled('ASSESSMENT_POSRTIONS_V2')) {
			redirect('dashboard', 'refresh');
		}

		$stdData = $this->parent_model->getClassIdOfLoggedInParent();
		// $assIds = $this->parent_model->getAssessmentIds($stdData->classId, $stdData->sectionId);
		$data['assessments'] = $this->parent_model->getPublishedAssessmentsOfStudent($stdData->classId, $stdData->sectionId);
		// echo "<pre>"; print_r($assIds);die();
		$data['main_content'] = 'parent/assessments/portion_index';
		$this->load->view('inc/template', $data);
	}

	public function showPortions($pId)
	{
		$data['pData'] = $this->parent_model->getPortions($pId);
		// echo "<pre>"; print_r($data['pData']);die();
		$data['main_content'] = 'parent/assessments/portions';
		$this->load->view('inc/template', $data);
	}

	public function reportAnalysis()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$cardData = $this->parent_model->getAllAssessmentsOfTemplate($studentId);
		$assIds = array();
		foreach ($cardData as $key => $value) {
			$assessments = json_decode($value->assessments);
			foreach ($assessments as $key => $value) {
				if (!in_array($value->id, $assIds))
					array_push($assIds, $value->id);
			}
		}
		$data['assessments'] = $assIds;
		$data['subjects'] = $this->parent_model->getSubjectsUnion($assIds);
		$data['main_content'] = 'parent/marks_cards/analysis_report';
		$this->load->view('inc/template', $data);
		// echo "<pre>"; print_r($assIds); die();
	}

	public function gradingSystem($tempId)
	{
		$template = $this->parent_model->assInTemplate($tempId);
		$data['template'] = $template;
		$gIds = explode(",", $template->grading_systems);
		$data['gradingSystems'] = $this->parent_model->getGradingSystem($gIds);

		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'parent/marks_cards/grading_systems_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/marks_cards/grading_systems_mobile';
		} else {
			$data['main_content']    = 'parent/marks_cards/grading_systems';
		}

		// $data['main_content'] = 'parent/marks_cards/grading_systems';
		$this->load->view('inc/template', $data);
		// echo "<pre>"; print_r($data['gradingSystems']); die();
	}

	public function savePdf($id)
	{
		$link = $this->parent_model->downloadMarksCard($id);
		$mc_created_date = $this->parent_model->get_markscard_created_date($id);

		//We moved from AWS to Wasabi around this time. The old data stayed in AWS.
		$date1 = new DateTime('2021-05-31');
		$date2 = new DateTime($mc_created_date);

		if ($date2 < $date1) {
			$url = $this->filemanager->getOldFilePath($link);
		} else {
			$url = $this->filemanager->getFilePath($link);
		}
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download('marks_card.pdf', $data, TRUE);
	}

	public function downloadCircular($id)
	{
		$link = $this->parent_model->downloadCircularAttachment($id);
		$url = $this->filemanager->getFilePath($link);
		$names = explode("/", $url);
		$filename = $names[count($names) - 1];
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($filename, $data, TRUE);
		// force_download('circular.pdf', $data);
	}

	public function downloadCircularV2($id)
	{
		$link = $this->parent_model->downloadCircularV2Attachment($id);
		$url = $this->filemanager->getFilePath($link);
		$names = explode("/", $url);
		$filename = $names[count($names) - 1];
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($filename, $data, TRUE);
		// force_download('circular.pdf', $data);
	}

	public function acknowledge()
	{
		$cardId = $_POST['id'];
		echo json_encode($this->parent_model->acknowledge($cardId));
	}

	public function getAnalysisData()
	{
		$entityId = $_POST['entityId'];
		$assessments = $_POST['assessments'];
		$ass = $this->parent_model->getAssDetails($assessments);
		$assIds = array();

		//getting all the assessments inside derived assessments
		foreach ($ass as $key => $value) {
			if (!in_array($value->id, $assIds)) {
				array_push($assIds, $value->id);
			}
			if ($value->generation_type == 'Auto') {
				$jData = json_decode($value->formula);
				foreach ($jData->assessments as $k => $val) {
					if (!in_array($val->id, $assIds)) {
						array_push($assIds, $val->id);
					}
				}
			}
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$marks = $this->parent_model->getMarks($studentId, $assIds, $entityId);
		// echo "<pre>"; print_r($marks);die();
		echo json_encode($marks);
	}

	public function change_username()
	{
		$data['user_provision'] = $this->parent_model->fetch_provision_username();
		//echo "<pre>"; print_r($data['user_provision']); die();
		$data['main_content'] = 'parent/change_username/index';
		$this->load->view('inc/template', $data);
	}

	public function checkUsernameparent()
	{
		$username = $_POST['username'];
		$result = $this->parent_model->checkUsernamebyparentwise($username);
		if ($result == '1') {
			echo '<span style="color:red;text-align:center;">Username already exists. Choose a different unique name.</span>';
		}
	}

	// HomeWork Module start from this method

	public function homework_view($filter="")
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$studentData = $this->parent_model->getStudentDataById($studentId);
		// echo "<pre>"; print_r($studentData);
		$data['studentData'] = $studentData;
		$data['section'] = $studentData->className . $studentData->sectionName;
		$data['section_id'] = $studentData->sectionId;

		if ($this->mobile_detect->isTablet()) {
			
			$data['filter'] = $filter;
			$data['homeWork'] = $this->parent_model->getHomeworkMobile($studentData->sectionId,$filter);
			$data['main_content'] = 'parent/homework/mobile_index';
		} else if ($this->mobile_detect->isMobile()) {
		    $data['filter'] = $filter;
			$data['homeWork'] = $this->parent_model->getHomeworkMobile($studentData->sectionId,$filter);
			$data['main_content'] = 'parent/homework/mobile_index';
		} else {
			$data['date'] = date('d-m-Y');
			$data['sevenDaysAgo'] = date('d-m-Y', strtotime('-7 day'));

			$data['homework'] = $this->parent_model->getHomeWork($studentData->sectionId, $data['sevenDaysAgo'], $data['date']);
			// echo '<pre>'; print_r($data['homework']); die();
			$data['main_content'] = 'parent/homework/desktop_index';
		}
		
		// if ($this->mobile_detect->isMobile()) {
			// 	$data['homeWork'] = $this->parent_model->getHomeworkMobile($studentData->sectionId);
			// 	$data['main_content'] = 'parent/homework/mobile_index';
			// } else {
				// 	$data['date'] = date('d-m-Y');
				// 	$data['sevenDaysAgo'] = date('d-m-Y', strtotime('-7 day'));
				
		// 	$data['homework'] = $this->parent_model->getHomeWork($studentData->sectionId, $data['sevenDaysAgo'], $data['date']);
		// 	// echo '<pre>'; print_r($data['homework']); die();
		// 	$data['main_content'] = 'parent/homework/desktop_index';
		// }
		
		$this->load->view('inc/template', $data);
	}

	public function getMultiImage()
	{
		$home = new parent_model;
		$studentId = $home->getStudentIdOfLoggedInParent();
		$studentData = $home->getStudentDataById($studentId);
		$date1 = $this->input->post('date1');
		$date2 = $this->input->post('date2');
		$data = $this->parent_model->getHomeWork($studentData->sectionId, $date1, $date2);
		foreach ($data as $key => $value) {
			$enc   = mb_detect_encoding($value->body);
			$data[$key]->body =  iconv($enc, 'UTF-8', $value->body);
		}
		// echo $date1; die();
		/*$data = $home->getMultipleImage($studentData->sectionId, $date1, $date2);
		foreach ($data as $key => $value) {
			if ($value->image != '') $value->image = $this->filemanager->getFilePath($value->image);
			$value->date =  date('d-m-Y-D', strtotime($value->date));
		}*/

		// echo "<pre>"; print_r($data);die();
		echo json_encode($data);
	}


	//Student Tasks module start with this method
	public function student_task_view()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$modules = json_decode($this->settings->getSetting('deactivation_modules'));
		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$data['is_deactivated'] = 0;
		if(!empty($modules)){
			if ($is_deactivated && in_array('Student Task', $modules)) {
				$data['is_deactivated'] = 1;
			}
		}
		
		// echo "<pre>"; print_r($data['is_deactivated']); die();

		$studentData = $this->parent_model->getStudentDataById($studentId);
		$lesson_config = $this->settings->getSetting('lesson_plan');
		if ($lesson_config) {
			$data['lp_task_max_submit_file'] = $lesson_config->lp_task_max_submit_file;
			$data['size'] = $lesson_config->size;
		} else {
			$data['lp_task_max_submit_file'] = "2";
			$data['size'] = "5MB";
		}
		$data['subjects'] = $this->parent_model->getSubjectsList($studentData->classId);
		$data['studentData'] = $studentData;
		// $data['aws'] = $this->get_chunk_upload_secret();
		// $this->load->library('aws_library');
		// $data['aws'] = $this->aws_library->getSignatureData();
		$data['section'] = $studentData->className . $studentData->sectionName;
		$data['section_id'] = $studentData->sectionId;
		// echo "<pre>";print_r($data);die();

		if ($this->mobile_detect->isTablet()) {
			$data['task'] = $this->parent_model->getStudentTasksMobile($studentId);
			$data['main_content'] = 'parent/student_tasks/tablet_index';
		} else if ($this->mobile_detect->isMobile()) {
			$data['task'] = $this->parent_model->getStudentTasksMobile($studentId);
			$data['main_content'] = 'parent/student_tasks/mobile_index';
		} else {
			$data['task'] = $this->parent_model->getStudentTasksMobile($studentId);
			$data['main_content'] = 'parent/student_tasks/desktop_index1';
		}

		$this->load->view('inc/template', $data);
	}

	public function getYouTubeVideo()
	{
		$resource = $this->parent_model->getResourceToPlay();
		$data['resources'] = array();
		if (sizeof($resource) != 0) {
			foreach ($resource as $key => $value) {
				if ($value->resource_file != '') {
					array_push($data['resources'], array('id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => $value->resource_file));
				}
			}
		}
		echo json_encode($data);
	}

	public function getFilteredStudentsTasks()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['task'] = $this->parent_model->getFilteredStudentsTasks($studentId);
		echo json_encode($data);
	}

	public function filterTasks()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent(); //
		$data['tasks'] = $this->parent_model->filterTasks($studentId);
		// echo '<pre>'; print_r($data['tasks']); die();
		
		if(!empty($data['tasks'])) {
			foreach($data['tasks'] as $key => $val) {
				$val->created_on_new= local_time($val->created_on_new, 'd M Y h:i A');
			}
		}
		echo json_encode($data);

		
	}

	public function getFilteredStudentsTasksDesktop()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['task'] = $this->parent_model->getFilteredStudentsTasksDesktop($studentId);
		echo json_encode($data);
	}
	public function getFilteredStudentsTasksDesktopOverdue()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['task'] = $this->parent_model->getFilteredStudentsTasksDesktopOverdue($studentId);
		echo json_encode($data);
	}

	public function view_student_task($task_id)
	{
		$this->load->model('student_tasks/Tasks_model', 'task');
		$data['task_id'] = $task_id;
		$lesson_config = $this->settings->getSetting('lesson_plan');
		if ($lesson_config) {
			$data['lp_task_max_submit_file'] = $lesson_config->lp_task_max_submit_file;
			$data['size'] = $lesson_config->size;
		} else {
			$data['lp_task_max_submit_file'] = "2";
			$data['size'] = "5MB";
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$studentData = $this->parent_model->getStudentDataById($studentId);
		$data['studentData'] = $studentData;
		$data['section'] = $studentData->className . $studentData->sectionName;
		$data['section_id'] = $studentData->sectionId;
		$data['task'] = $this->parent_model->getSingleStudentTask($task_id, $studentId);
		if(empty($data['task'])) {
			$this->session->set_flashdata('flashError', 'Somethintg went wrong. Please try again!');
			redirect('Parent_controller/student_task_view');
		}
		if ($data['task'][0]->read_status == 'unread') {
			$update_read_status = $this->parent_model->makeTaskRead($data['task'][0]->task_student_id);
		}
		// $update_read_status = $this->parent_model->updateReadStatus($studentId,$task_id);
		$data['submission_enabled'] = $this->settings->isParentModuleEnabled('HOMEWORK_SUBMISSION');
		$data['resources'] = array();
		if (!empty(json_decode($data['task'][0]->resource_ids))) {
			
			$resources = $this->task->getSelectedResources(json_decode($data['task'][0]->resource_ids));
			if (sizeof($resources) != 0) {
				foreach ($resources as $key => $value) {
					if ($value->resource_type == 'Video Link' || $value->resource_type == 'Hyper Link') {
						//Resource is a video link
						array_push($data['resources'], array('resource_id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => $value->resource_file, 'original_link' => $value->original_link));
					} else if ($value->resource_type === 'Vimeo') {
						array_push($data['resources'], array('resource_id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => $value->resource_file, 'original_link' => $value->original_link));
					} else {
						//File is stored in S3
						if ($value->resource_file != '') {
							// $this->filemanager->getSignedUrlWithExpiry($file_link->resource_file, '+5 minutes')
							array_push($data['resources'], array('resource_id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => $this->filemanager->getSignedUrlWithExpiry($value->resource_file,'+5 minutes')));
						} else {
							array_push($data['resources'], array('resource_id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => 'No Files'));
						}
					}
				}
			}
		}
		if(!empty($data['task'][0]->task_file_path)){
			$attachments = json_decode($data['task'][0]->task_file_path);
			foreach ($attachments as $key => $value) {
				array_push($data['resources'], array('resource_id' => 1, 'name' => 'Document', 'type' => 'Others', 'path' => $this->filemanager->getFilePath($value->path)));
			}
		}
		$data['submitted_files'] = array();
		$submitted_files = $this->parent_model->getSubmittedFiles($data['task'][0]->task_student_id);
		if (sizeof($submitted_files) != 0) {
			foreach ($submitted_files as $key => $value) {
				if ($value->file_path != '') {
					array_push($data['submitted_files'], array('file_id' => $value->id, 'file_order' => $value->file_order, 'file_name' => $value->file_name,  'file_path' => $this->filemanager->getFilePath($value->file_path)));
				}
			}
		}
		$data['evaluated_files'] = array();
		$evaluated_files = $this->parent_model->getEvaluatedFiles($data['task'][0]->task_student_id);
		if (sizeof($evaluated_files) != 0) {
			foreach ($evaluated_files as $key => $value) {
				if ($value->file_path != '') {
					array_push($data['evaluated_files'], array('file_id' => $value->id, 'file_order' => $value->file_order, 'file_name' => $value->file_name,  'file_path' => $this->filemanager->getFilePath($value->file_path)));
				}
			}
		}
		// echo "<pre>";print_r($data);die();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'parent/student_tasks/view_task_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content'] = 'parent/student_tasks/view_task_mobile';
		}


		// if ($this->mobile_detect->isMobile()) {
		// 	$data['main_content'] = 'parent/student_tasks/view_task_mobile';
		// }
		$this->load->view('inc/template', $data);
	}

	public function getTaskDetailsById()
	{
		$this->load->model('student_tasks/Tasks_model', 'task');
		$task_id = $_POST['task_id'];
		$is_read = $_POST['is_read'];
		$task_student_id = $_POST['task_student_id'];
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		if (!$is_read) {
			$this->parent_model->makeTaskRead($task_student_id);
		}
		$data['task'] = $this->parent_model->getStudentTaskData($task_student_id);
		$data['task']->created_on= local_time($data['task']->created_on, 'd M Y h:i A');
		$data['task']->submission_on_new= local_time($data['task']->submission_on, 'd M Y h:i A');
		$submitted_files = $this->task->getSubmittedFilesV2($task_student_id);
		$data['submitted_files'] = array();
		if (sizeof($submitted_files) != 0) {
			foreach ($submitted_files as $key => $value) {
				if ($value->file_path != '') {
					$file = array();
					$f = explode('.', $value->file_path);
					$f_type = strtolower($f[count($f) - 1]);
					$file['file_path'] = $this->filemanager->getFilePath($value->file_path);
					$file['file_order'] = $value->file_order;
					$file['file_id'] = $value->id;
					$file['file_name'] = $value->file_name;
					$file['file_type'] = $f_type;
					$file['evaluation_id'] = $value->evaluation_id;
					if ($value->evaluation_file_path != '') {
						$ef = explode('.', $value->evaluation_file_path);
						$ef_type = strtolower($f[count($f) - 1]);
						$file['evaluation_file_type'] = $ef_type;
						$file['evaluation_file_path'] = $this->filemanager->getFilePath($value->evaluation_file_path);
						$file['evaluation_file_id'] = $value->evaluation_file_id;
						$file['evaluation_file_name'] = $value->evaluation_file_name;
					}
					array_push($data['submitted_files'], $file);
				}
			}
		}

		$data['resources'] = array();
		if (!empty($data['task']->resource_ids)) {
			$resources = $this->task->getSelectedResources(json_decode($data['task']->resource_ids));
			if (sizeof($resources) != 0) {
				foreach ($resources as $key => $value) {
					if ($value->resource_type == 'Video Link'  || $value->resource_type == 'Hyper Link') {
						array_push($data['resources'], array('resource_id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => $value->resource_file, 'original_link' => $value->original_link));
					} else if ($value->resource_type === 'Vimeo') {
						array_push($data['resources'], array('resource_id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => $value->resource_file, 'original_link' => $value->original_link));
					} else {
						//File is stored in S3
						if ($value->resource_file != '') {
							array_push($data['resources'], array('resource_id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => $this->filemanager->getSignedUrlWithExpiry($value->resource_file, '+5 minutes')));
						} else {
							array_push($data['resources'], array('resource_id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => 'No Files'));
						}
					}
				}
			}
		}

		if(!empty($data['task']->task_file_path)){
			$attachments = json_decode($data['task']->task_file_path);
			foreach ($attachments as $key => $value) {
				$f = explode('.', $value->name);
				$file_type = strtolower($f[count($f) - 1]);
				// echo '<pre>';print_r($value);die();
				array_push($data['resources'], array('resource_id' => 1, 'file_type' => $file_type, 'name' => "$value->name", 'type' => 'Others', 'path' => $this->filemanager->getSignedUrlWithExpiry($value->path, '+5 minutes')));
			}
		}

		// echo '<pre>';print_r($data['task']);die();

		echo json_encode($data);
	}

	public function download(){
        $input = $this->input->get();

        // $ext = explode('.',$input['path']);
		$signed_resource = $this->filemanager->getSignedUrlWithExpiry($input['path'], '+5 minutes');

		$file = explode("/", $input['path']);
		$file_name = 'document';
		$fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
		//Download the file
		$data = file_get_contents($signed_resource);
		$this->load->helper('download');
		force_download($fname, $data, TRUE);
		$this->load->library('user_agent');
		redirect($this->agent->referrer());	

		// echo $input['path'];
		// echo $ext;

		// die();

        // $extn = $ext[count($ext)-1];


        // $data = file_get_contents($input['path']);
        // //load download helper
        // $this->load->helper('download');
        // //download file from directory
        // force_download('document.'.$extn, $data, TRUE);
		// $this->load->library('user_agent');
		// redirect($this->agent->referrer());	
    }

	/* NOT USING THIS FUNCTION
	public function getSingleTaskDetailsDesktop(){
		$task_id = $_POST['task_id'];
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$studentData = $this->parent_model->getStudentDataById($studentId);
		$data['studentData'] = $studentData;
		$data['section'] = $studentData->className . $studentData->sectionName;
		$data['section_id'] = $studentData->sectionId;
		$data['task'] = $this->parent_model->getSingleStudentTask($task_id,$studentId);
		$update_read_status = $this->parent_model->makeTaskRead($data['task'][0]->task_student_id);
		// $update_read_status = $this->parent_model->updateReadStatus($studentId,$task_id);
		$data['submission_enabled'] = $this->settings->isParentModuleEnabled('HOMEWORK_SUBMISSION');
		$data['submitted_files'] = array();
		$submitted_files = $this->parent_model->getSubmittedFiles($data['task'][0]->task_student_id);
	        if(sizeof($submitted_files)!=0) {
	          foreach ($submitted_files as $key =>$value) {
	          	if($value->file_path!=''){
	            	array_push($data['submitted_files'], array('file_id'=>$value->id,'file_order'=>$value->file_order,'file_name'=>$value->file_name,  'file_path' => $this->filemanager->getFilePath($value->file_path)));
	          	}
	          }
					}
					$data['evaluated_files'] = array();
					$evaluated_files = $this->parent_model->getEvaluatedFiles($data['task'][0]->task_student_id);
								if(sizeof($evaluated_files)!=0) {
									foreach ($evaluated_files as $key =>$value) {
										if($value->file_path!=''){
											array_push($data['evaluated_files'], array('file_id'=>$value->id,'file_order'=>$value->file_order,'file_name'=>$value->file_name,  'file_path' => $this->filemanager->getFilePath($value->file_path)));
										}
									}
								}
		$data['resources'] = array();
		if(!empty(json_decode($data['task'][0]->resource_ids))){
			$resources = $this->task->getSelectedResources(json_decode($data['task'][0]->resource_ids));
	        if(sizeof($resources)!=0) {
	          foreach ($resources as $key =>$value) {
	          	if($value->resource_type=='Video Link'  || $value->resource_type=='Hyper Link'){
								//Resource is a video link
								array_push($data['resources'], array('resource_id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $value->resource_file, 'original_link' => $value->original_link));
							} else {
								//File is stored in S3
								if($value->resource_file!=''){
									array_push($data['resources'], array('resource_id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $this->filemanager->getFilePath($value->resource_file)));
								}
								else{
									array_push($data['resources'], array('resource_id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => 'No Files'));
								}	
							}
	          }
					}
				
	    }
		echo json_encode($data);
	}*/

	/* NOT USING THIS FUNCTION
	public function getTaskSubmissionForm(){
		$task_id = $_POST['task_id'];
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['task'] = $this->parent_model->getSingleStudentTask($task_id,$studentId);
		echo json_encode($data);
	}*/

	public function getResourceToPlay()
	{
		$resource = $this->parent_model->getResourceToPlay();
		$data['resources'] = array();
		if (sizeof($resource) != 0) {
			foreach ($resource as $key => $value) {
				if ($value->resource_file != '') {
					array_push($data['resources'], array('id' => $value->id, 'name' => $value->name, 'type' => $value->resource_type, 'path' => $this->filemanager->getFilePath($value->resource_file)));
				}
			}
		}
		echo json_encode($data);
	}
	//Homework module end 

	public function parent_users_name_submit($id)
	{
		$result = $this->parent_model->update_parent_username_update($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully username change');
			// redirect('dashboard');
			$this->ion_auth->logout();
			redirect('auth/login', 'refresh');
		} else {
			$this->session->set_flashdata('flashError', 'Username already exists. Choose a different unique name.');
			redirect('dashboard');
		}
	}

	public function saveRecordedAudio()
	{
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$file = $this->__s3FileUpload($_FILES['audio']);
		$task_id = $_POST['task_id'];

		if ($file['file_name'] == '') {
			echo 0;
		} else {
			$lp_task_id = $this->db->select('lp.id as lp_task_student_id, t.task_name')
				->from('lp_tasks_students lp')
				->join('lp_tasks t', 't.id=lp.lp_tasks_id')
				->where('lp.student_id', $student_id)
				->where('lp.lp_tasks_id', $task_id)
				->get()->row();
			$student_task_id = $lp_task_id->lp_task_student_id;
			$files_data[] = array(
				'lp_tasks_students_id' => $student_task_id,
				'file_path' => $file['file_name'],
				'file_order' => 1,
				'type' => 'Submission',
				'file_name' => $lp_task_id->task_name
			);

			$status = $this->parent_model->submit_task($student_task_id, $files_data, $task_id);
			echo $status;
		}
	}

	public function help_page_index()
	{
		$data['main_content']    = 'helptext/index';
		$this->load->view('inc/template', $data);
	}

	public function aboutUs()
	{
		$data['main_content']    = 'helptext/aboutus';
		$this->load->view('inc/template', $data);
	}

	public function galleries()
	{
		$this->load->helper('text');
		$data['gallery_info'] = $this->parent_model->load_galleries();

		$data['main_content']    = 'parent/galleries/index';
		$this->load->view('inc/template', $data);
	}

	public function escort_authorization(){
		$data['main_content']    = 'parent/escort_auth/index';
		$this->load->view('inc/template', $data);
	}

	public function create_authorization(){
		$parent_id = $this->authorization->getAvatarStakeHolderId();
		$data['parent_id']=$parent_id;
		$logged_in_parent_name=$this->student_analytics_model->get_logged_in_parent_name($parent_id);
		$data['name']=$logged_in_parent_name->first_name;
		$data['main_content']    = 'parent/escort_auth/create/index';
		$this->load->view('inc/template', $data);
	}

	public function add_created_auth_data(){
		// echo "<pre>"; print_r($_POST); die();
		echo $this->student_analytics_model->add_created_auth_data($_POST);
	}

	public function accept_student_auth(){
		echo $this->student_analytics_model->accept_student_auth($_POST);
	}

	public function accept_authorization(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['students']= $this->student_analytics_model->get_stds($studentId);
		$data['main_content']    = 'parent/escort_auth/accept/index';
		$this->load->view('inc/template', $data);
	}

	public function view_authorization(){
		$data['main_content']    = 'parent/escort_auth/view/index';
		$this->load->view('inc/template', $data);
	}

	public function get_student_auth(){
		$result=$this->student_analytics_model->get_student_auth();
		echo json_encode($result);
	}

	public function get_parent_related_data(){
		$parent_id = $this->authorization->getAvatarStakeHolderId();
		//1. sibling details
		//2. parent details
		//3. gaurdings details
		//4. drivers details
		
		$details=$this->student_analytics_model->get_sibling_details_by_parent_id($parent_id);
		echo json_encode($details);
	}

	

	public function lms()
	{
		$published_sessions_for_students=$this->lessonplan_model->get_published_sessions_for_students();
		
		foreach($published_sessions_for_students as $key => $val){
			$val->session_detail=$this->lessonplan_model->get_session_details($val->session_id);
			$val->subject_class=$this->lessonplan_model->get_subject_name($val->session_detail->lp_lesson_id);
			$val->created_by_name=$this->Staff_Model->get_staff_name_from_avatar_id($val->published_by);
		}

		// echo "<pre>"; print_r($published_sessions_for_students); die();
		
		$data["published_sessions_for_students"]=$published_sessions_for_students;
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'parent/lms/home_page';
		 }else if($this->mobile_detect->isMobile()){
		   $data['main_content']    = 'parent/lms/home_page';
		 }else{
		   $data['main_content']    = 'parent/lms/home_page';        
		 }
		 
		$this->load->view('inc/template', $data);
	}

	public function view_lms($session_id){
		$data["enable_staff_teaching_plan_in_parent_lms_console"]=$this->settings->getSetting("enable_staff_teaching_plan_in_parent_lms_console");

		$data['session_id']=$session_id;
		$data['main_content'] = 'parent/lms/details/index';
		$this->load->view('inc/template', $data);
	}

	public function view_gallery($gallery_id)
	{
		$data['gallery_info'] = $this->parent_model->get_gallery_info($gallery_id);
		$data['image_info'] = $this->parent_model->get_images_info($gallery_id);
		// echo '<pre>';print_r($data['image_info']);
		$data['main_content'] = 'parent/galleries/view_gallery';
		$this->load->view('inc/template', $data);
	}

	public function gallery_wander($where, $gallary_id)
	{
		// echo $gallery_id; die();
		switch ($where) {
			case 'prev':
				$gallary_id = $this->parent_model->getPrevGallaryId($gallary_id);
				break;
			case 'next':
				$gallary_id = $this->parent_model->getNextGalleryId($gallary_id);
				break;
			default:
				break;
		}
	}

	public function gallery_wander_prev($gallery_id)
	{
		//echo "hello"; die();
	}

	public function init_fee_transaction()
	{
		$amount = $_POST['amount'];
		$source = 'fee_paid';
		$status = $this->payment->init_fee_transaction($amount, $source);

		if (!$status) {
			$this->session->set_flashdata('flashError', 'Unable to connect to the payment gateway. Try after sometime');
			redirect('parent_controller/parent_initiative');  // Change it to actual function

		} else {
			$this->session->set_flashdata('flashError', 'Something happened. Contact System administrator');
			redirect('parent_controller/parent_initiative');  // Change it to actual function
			//Code should not come here as there is a redirect call if it is successful
		}
	}

	private function _getJourneyIds($journeys)
	{
		$ids = [];
		foreach ($journeys as $key => $value) {
			array_push($ids, $value->journeyId);
		}
		return $ids;
	}

	private function makeStoredJourneys($journeys, $attendance)
	{

		$current_time = strtotime(date('d-m-Y H:i:s'));
		// $current_time = strtotime($this->parent_model->timezone_setter(date('d-m-Y H:i:s')));
		foreach ($journeys as $key => $journey) {
			$start = strtotime($journey->start_time);
			$end = strtotime($journey->end_time);
			$journey->tracking = 'Not Started';
			if ($start <= $current_time && $end >= $current_time) {
				$journey->tracking = 'Running';
			} else if ($end < $current_time) {
				$journey->tracking = 'Completed';
			}
			$attendance[] = $journey;
		}
		return $attendance;
	}

	private function makeAttendanceJourneys($journeys, $attendance, $stored_journeys)
	{
		
		// $current_time = strtotime($this->parent_model->timezone_setter(date('d-m-Y H:i:s')));
		$current_time = strtotime(date('d-m-Y H:i:s'));
		foreach ($journeys as $key => $att) {
			$att->journey_change = 'no';
			if (!in_array($att->journeyId, $stored_journeys)) {
				$att->journey_change = 'yes';
			}
			$start = strtotime($att->start_time);
			$end = strtotime($att->end_time);
			$att->tracking = 'Not Started';
			if ($start <= $current_time && $end >= $current_time) {
				$att->tracking = 'Running';
			} else if ($end < $current_time) {
				$att->tracking = 'Completed';
			}
			$attendance[] = $att;
		}		
		return $attendance;
	}

	public function transport()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$rfid = $this->parent_model->getRFID($studentId);
		$data['journeys'] = $this->parent_model->getTodaysJourney($studentId);
		$data['attendance'] = array();
		if ($rfid) {
			// $data['attendance'] = $this->parent_model->rfidJourney($rfid);
			$data['attendance'] = $this->parent_model->rfidJourneys($rfid);
			// echo "<pre>"; print_r($data); die();
			// $data['actual_picking'] = $this->_getJourneyIds($data['attendance']['PICKING']);
			// $data['actual_droping'] = $this->_getJourneyIds($data['attendance']['DROPPING']);
		}
		$attendance = array();
		$current_time = strtotime($this->parent_model->timezone_setter(date('d-m-Y H:i:s')));
		$data['is_attendance'] = 0;
		if (empty($data['attendance']['PICKING'])) {
			$attendance = $this->makeStoredJourneys($data['journeys']['PICKING'], $attendance);
		} else {
			$data['is_attendance'] = 1;
			$stored_picking = $this->_getJourneyIds($data['journeys']['PICKING']);
			$attendance = $this->makeAttendanceJourneys($data['attendance']['PICKING'], $attendance, $stored_picking);
		}

		if (empty($data['attendance']['DROPPING'])) {
			$attendance = $this->makeStoredJourneys($data['journeys']['DROPPING'], $attendance);
		} else {
			$data['is_attendance'] = 1;
			$stored_dropping = $this->_getJourneyIds($data['journeys']['DROPPING']);
			$attendance = $this->makeAttendanceJourneys($data['attendance']['DROPPING'], $attendance, $stored_dropping);
		}

		// foreach($attendance as $att){
		// 	$randomNumber = rand(1, 10000);
		// 	$trackingUrlWithRandom = $att->tracking_url . '&rand=' . $randomNumber;
		// 	$att->tracking_url = $trackingUrlWithRandom;
		// }

		$data['activities'] = $attendance;

		// echo '<pre>'; print_r($attendance); die();

		/*$current_journey = new stdClass();
		$current_time = strtotime($this->parent_model->timezone_setter(date('d-m-Y H:i:s')));
		$data['in_journey'] = 0;
		if(empty($attendance)) {
			foreach ($data['journeys']['PICKING'] as $key => $journey) {
				$start = strtotime($journey->start_time);
				$end = strtotime($journey->end_time);
				if($start <= $current_time && $end >= $current_time) {
					$data['in_journey'] = 1;
					$current_journey->journey_name = $journey->journey_name;
					$current_journey->stopId = $journey->stopId;
					$current_journey->thingId = $journey->thingId;
					$current_journey->thing_name = $journey->thing_name;
					$current_journey->start_time = $journey->start_time;
					$current_journey->end_time = $journey->end_time;
					$current_journey->status = 1;
				}
			}
			if(!$data['in_journey']) {
				foreach ($data['journeys']['DROPPING'] as $key => $journey) {
					$start = strtotime($journey->start_time);
					$end = strtotime($journey->end_time);
					if($start <= $current_time && $end >= $current_time) {
						$data['in_journey'] = 1;
						$current_journey->journey_name = $journey->journey_name;
						$current_journey->stopId = $journey->stopId;
						$current_journey->thingId = $journey->thingId;
						$current_journey->thing_name = $journey->thing_name;
						$current_journey->start_time = $journey->start_time;
						$current_journey->end_time = $journey->end_time;
						$current_journey->status = 1;
					} 
				}
			}
		}
		$data['current_journey'] = $current_journey;*/

		// echo "<pre>"; print_r($current_journey); die();
		$dont_show = $this->settings->getSetting('dont_show_bus_tracking');
		$data['show_numbers'] = 'none';
		$show_numbers = $this->settings->getSetting('display_transportation_numbers');
		if ($show_numbers) {
			$data['show_numbers'] = $show_numbers;
		}
		$data['show_tracking'] = 1;
		if ($dont_show == '') {
			$data['show_tracking'] = 1;
		} else {
			$data['show_tracking'] = $dont_show;
		}
		$data['main_content'] = 'parent/transport/info';
		// $data['main_content'] = 'parent/transport/index';
		$this->load->view('inc/template', $data);
	}

	public function transport_request(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['stopList'] = $this->parent_model->get_fee_Stop_list();
		$areaRoutes = [];
		foreach ($data['stopList'] as $key => $area) {
			if(!in_array($area->route, $areaRoutes, true)){
			array_push($areaRoutes, $area->route);
			}
		}
		$data['route_area'] = $areaRoutes;
		$data['kilometer'] = $this->parent_model->get_fee_km_list();

		$data['promotionAcadYearId'] = $this->acad_year->getPromotionAcadYearId();
		$data['promotionAcadYear'] = $this->acad_year->getPromotionAcadYear();

		$data['currentAcadYearId'] = $this->acad_year->getAcadYearId();
    	$data['currentAcadYear'] = $this->acad_year->getAcadYear();
    	$data['studentId'] = $studentId;
		$data['is_student_promoted'] = $this->parent_model->check_student_is_promoted($studentId,$data['promotionAcadYearId']);
		$data['pickup_end_points'] = $this->settings->getSetting('transport_picup_and_end_point_enabled');
		$data['main_content'] = 'parent/transport/transport_request';
		$this->load->view('inc/template', $data);
	}

	/*public function track_bus($thingId)
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['tracking_url'] = $this->parent_model->getTrackingUrlByThing($thingId);
		$data['main_content'] = 'parent/transport/track_bus';
		$this->load->view('inc/template', $data);
	}*/

	public function track_bus_noti($thingId, $stopId = 0)
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$url = $this->parent_model->getTrackingUrlByThing($thingId);
		if ($stopId != 0) {
			$url .= '&stopId=' . $stopId;
		}
		$data['tracking_url'] = $url;
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'parent/transport/track_bus_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content'] = 'parent/transport/track_bus_mobile';
		} else {
			$data['main_content'] = 'parent/transport/track_bus';
		}
		$this->load->view('inc/template', $data);
	}

	public function track_bus()
	{
		$thingId = $_POST['thing_id'];
		$stopId = $_POST['stop_id'];
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$url = $this->parent_model->getTrackingUrlByThing($thingId);
		$data['tracking_url'] = $url . '&stopId=' . $stopId;
		$data['main_content'] = 'parent/transport/track_bus';
		$this->load->view('inc/template', $data);
	}

	public function edit_profile_parent($callFrom = '')
	{
		$data['callFrom'] = $callFrom;
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_id'] = $studentId;

		$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
		$data['stops'] = $this->parent_model->getFeesStops();
		if ($data['studentData']->is_placeholder == 1) {
			$data['studentData']->sectionName = '';
		} else {
			$data['studentData']->sectionName = ' / ' . $data['studentData']->sectionName;
		}
		//Get Student details
		$student_address_types = $this->settings->getSetting('student_address_types');
		$ins = $this->settings->getSetting('parent_profile_instructions');
		$data['instructions'] = array();
		if ($ins && $ins != '[]') {
			$data['instructions'] = json_decode($ins);
		}

		$sAddress = [];
		if (!empty($student_address_types)) {
			foreach ($student_address_types as $key => $address) {
				// $sAddress[$address] = $this->parent_model->getStudent_Address_Details($studentId, $key);
				$s_add = $this->parent_model->getStudent_Address_Details($studentId, $key);
				$sAddress[$address] = array();
				if (!empty($s_add))
					$sAddress[$address] = $s_add[0];
			}
		}

		//Get Father details
		// $profile_display_father = $this->settings->getSetting('parent_profile_display_father');
		$father_address_types = $this->settings->getSetting('father_address_types');
		$data['fatherData'] = $this->parent_model->getFatherDetails($studentId);
		// echo "<pre>"; print_r($data['fatherData']); die();
		$fAddress = [];
		if (!empty($father_address_types)) {
			foreach ($father_address_types as $key => $address) {
				// $fAddress[$address] = $this->parent_model->getFather_Address_Details($data['fatherData']->id,$key);
				$f_add = $this->parent_model->getFather_Address_Details($data['fatherData']->id, $key);
				$fAddress[$address] = array();
				if (!empty($f_add))
					$fAddress[$address] = $f_add[0];
			}
		}
		//Get Mother details
		$mother_address_types = $this->settings->getSetting('mother_address_types');
		$data['motherData'] = $this->parent_model->getMotherDetails($studentId);
		$mAddress = [];
		if (!empty($mother_address_types)) {
			foreach ($mother_address_types as $key => $address) {
				// $mAddress[$address] = $this->parent_model->getFather_Address_Details($data['motherData']->id,$key);
				$m_add = $this->parent_model->getFather_Address_Details($data['motherData']->id, $key);
				$mAddress[$address] = array();
				if (!empty($m_add))
					$mAddress[$address] = $m_add[0];
			}
		}
		// $guardian_fields = $this->settings->getSetting('parent_profile_display_guardian');
		$data['show_guardian'] = 0;
		if($this->settings->isProfile_profile_enabled('GUARDIAN_PHOTO') || $this->settings->isProfile_profile_enabled('GUARDIAN_NAME') || $this->settings->isProfile_profile_enabled('GUARDIAN_CONTACT_NO') || $this->settings->isProfile_profile_enabled('GUARDIAN_EMAIL')){
		// if ($guardian_fields && $guardian_fields->display == 1) {
			$data['show_guardian'] = 1;
			$data['guardianData'] = $this->parent_model->getGuardianDetails($studentId);
			// echo "Fields: <pre>"; print_r($data['guardianData']); die();
		}
		/*echo '<pre>'; print_r($sAddress);
		echo '<pre>'; print_r($fAddress);
		echo '<pre>'; print_r($mAddress);die();*/
		$data['fatherAddress'] = $fAddress;
		$data['motherAddress'] = $mAddress;
		$data['studentAddress'] = $sAddress;
		$data['main_content'] = 'parent/profile/profile_edit';
		$this->load->view('inc/template', $data);
	}

	private function _resize_image($file, $max_resolution, $type)
	{
		if (file_exists($file)) {
			if ($type == 'image/jpeg')
				$original_image = imagecreatefromjpeg($file);
			else
				$original_image = imagecreatefrompng($file);

			//check orientation 
			// $exif = exif_read_data($file);

			try {
				$exif = exif_read_data($file);
			} catch (Exception $exp) {
				$exif = false;
			}

			if ($exif) {
				if (!empty($exif['Orientation'])) {
					switch ($exif['Orientation']) {
						case 3:
							$original_image = imagerotate($original_image, 180, 0);
							break;

						case 6:
							$original_image = imagerotate($original_image, -90, 0);
							break;

						case 8:
							$original_image = imagerotate($original_image, 90, 0);
							break;
					}
				}
			}

			//resolution
			$original_width = imagesx($original_image);
			$original_height = imagesy($original_image);

			//try width first
			$ratio = $max_resolution / $original_width;
			$new_width = $max_resolution;
			$new_height = $original_height * $ratio;

			//if that dosn't work
			if ($new_height > $max_resolution) {
				$ratio = $max_resolution / $original_height;
				$new_height = $max_resolution;
				$new_width = $original_width * $ratio;
			}

			if ($original_image) {
				$new_image = imagecreatetruecolor($new_width, $new_height);
				imagecopyresampled($new_image, $original_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
				if ($type == 'image/jpeg')
					imagejpeg($new_image, $file);
				else
					imagepng($new_image, $file);
			}

			return $file;
			// echo '<br>Resized: ';
			// echo filesize($file); 

			// echo '<pre>'; print_r($file); die();
		}
	}

	public function updateStudent_profile_edit()
	{
		// echo "<pre>"; print_r($_FILES['student_photo']); die();
		$input_form = $this->input->post();
		$grouped_input = $this->_prepareStudentInput($input_form);
		// if the photo file name is passed as empty, the update function will not update the photo
		if (!empty($_FILES['student_photo'])) {
			$min_size = $this->_resize_image($_FILES['student_photo']['tmp_name'], 200, $_FILES['student_photo']['type']);

			$picture = array('tmp_name' => $min_size, 'name' => $min_size);
			$sResigedPhoto = $this->s3FileUpload($picture);
			$this->session->userdata('parentCache')->picture_url = $sResigedPhoto['file_name'];
			$sRealPhoto = $this->s3FileUpload($_FILES['student_photo']);
		} else {
			$sResigedPhoto['file_name'] = '';
			$sRealPhoto['file_name'] = '';
		}

		if (!empty($_FILES['father_photo'])) {
			$min_size = $this->_resize_image($_FILES['father_photo']['tmp_name'], 200, $_FILES['father_photo']['type']);
			$picture = array('tmp_name' => $min_size, 'name' => $min_size);
			$fResigedPhoto = $this->s3FileUpload($picture);
			$fRealPhoto = $this->s3FileUpload($_FILES['father_photo']);
		} else {
			$fResigedPhoto['file_name'] = '';
			$fRealPhoto['file_name'] = '';
		}

		/*if (!empty($_FILES['father_photo'])) {
	    	$fPhoto =$this->s3FileUpload($_FILES['father_photo']);
	    }else{
	    	$fPhoto['file_name'] = '';
	    }*/

		if (!empty($_FILES['mother_photo'])) {
			$min_size = $this->_resize_image($_FILES['mother_photo']['tmp_name'], 200, $_FILES['mother_photo']['type']);
			$picture = array('tmp_name' => $min_size, 'name' => $min_size);
			$mResigedPhoto = $this->s3FileUpload($picture);
			$mRealPhoto = $this->s3FileUpload($_FILES['mother_photo']);
		} else {
			$mResigedPhoto['file_name'] = '';
			$mRealPhoto['file_name'] = '';
		}

		/*if (!empty($_FILES['mother_photo'])) {
	    	$mPhoto =$this->s3FileUpload($_FILES['mother_photo']);
	    }else{
	    	$mPhoto['file_name'] = '';
	    }*/

		$result = $this->parent_model->update_profile_edit_parents($grouped_input['student'], $grouped_input['father'], $grouped_input['mother'], $sResigedPhoto, $sRealPhoto, $fResigedPhoto, $fRealPhoto, $mResigedPhoto, $mRealPhoto);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Details updated successfully');
		} else {
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('parent_controller/profile');
	}

	private function _prepareStudentInput(&$input)
	{
		$return_data = [];
		foreach ($input as $k => $v) {
			$start_key = substr($k, 0, 2);
			if ($start_key == 'f_') {
				$key = str_replace("f_", "", $k);
				$return_data['father'][$key] = $v;
			} elseif ($start_key == 'm_') {
				$key = str_replace("m_", "", $k);
				$return_data['mother'][$key] = $v;
			} else {
				$return_data['student'][$k] = $v;
			}
		}
		return $return_data;
	}


	public function s3FileUpload($file, $folder = 'profile')
	{
		if ($file['tmp_name'] == '' || $file['name'] == '') {
			return ['status' => 'empty', 'file_name' => ''];
		}
		return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder);
	}

	public function s3FileUploadAsPrivate($file, $folder = 'profile')
	{
		if ($file['tmp_name'] == '' || $file['name'] == '') {
			return ['status' => 'empty', 'file_name' => ''];
		}
		return $this->filemanager->uploadFileAsPrivate($file['tmp_name'], $file['name'], $folder);
	}

	public function edit_profile_parent_mobile($callFrom = ''){
		$data['callFrom'] = $callFrom;
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_id'] = $studentId;

		$data['studentData'] = $this->parent_model->getStudentDataById($studentId);
		if(!empty($data['studentData']->category)){
			$data['studentData']->category = $this->settings->getSetting('category')[$data['studentData']->category];
		}
		$data['stops'] = $this->parent_model->getFeesStops();
		if ($data['studentData']->is_placeholder == 1) {
			$data['studentData']->sectionName = '';
		} else {
			$data['studentData']->sectionName = ' / ' . $data['studentData']->sectionName;
		}
		//Get Student details
		$student_address_types = $this->settings->getSetting('student_address_types');
		$ins = $this->settings->getSetting('parent_profile_instructions');
		$data['instructions'] = array();
		if ($ins && $ins != '[]') {
			$data['instructions'] = json_decode($ins);
		}

		$sAddress = [];
		if (!empty($student_address_types)) {
			foreach ($student_address_types as $key => $address) {
				$sAddress[$address] = $this->parent_model->getStudent_Address_Details($studentId, $key);
			}
		}

		//Get Father details
		// $profile_display_father = $this->settings->getSetting('parent_profile_display_father');
		$father_address_types = $this->settings->getSetting('father_address_types');

		$data['fatherData'] = $this->parent_model->getFatherDetails($studentId);
		// echo "<pre>"; print_r($data['fatherData']); die();
		$fAddress = [];
		if (!empty($father_address_types)) {
			foreach ($father_address_types as $key => $address) {
				$fAddress[$key][$address] = $this->parent_model->getFather_Address_Details($data['fatherData']->id, $key);
			}
		}


		//Get Mother details
		$mother_address_types = $this->settings->getSetting('mother_address_types');
		$data['motherData'] = $this->parent_model->getMotherDetails($studentId);
		$mAddress = [];
		if (!empty($mother_address_types)) {
			foreach ($mother_address_types as $key => $address) {
				$mAddress[$key][$address] = $this->parent_model->getFather_Address_Details($data['motherData']->id, $key);
			}
		}

		// $guardian_fields = $this->settings->getSetting('parent_profile_display_guardian');
		$data['show_guardian'] = 0;
		if($this->settings->isProfile_profile_enabled('GUARDIAN_PHOTO') || $this->settings->isProfile_profile_enabled('GUARDIAN_NAME') || $this->settings->isProfile_profile_enabled('GUARDIAN_CONTACT_NO') || $this->settings->isProfile_profile_enabled('GUARDIAN_EMAIL')){
		// if ($guardian_fields && $guardian_fields->display == 1) {
			$data['show_guardian'] = 1;
			$data['guardianData'] = $this->parent_model->getGuardianDetails($studentId);
			// echo "Fields: <pre>"; print_r($data['guardianData']); die();
		}

		$data['fatherAddress'] = $fAddress;
		$data['motherAddress'] = $mAddress;
		$data['studentAddress'] = $sAddress;
		$data['main_content'] = 'parent/profile/mobile_profile_edit';
		$this->load->view('inc/template', $data);
	}

	public function save_profile_photo()
	{
		$input = $_POST;
		$file = $_FILES['file'];
		$sRealphoto = $this->parent_model->update_student_profile_photo($input['type'], $input['id'], $input['high_quality'], 'HIGH_QUALITY');
		if ($sRealphoto && $input['type'] !='family') {
			$min_size = $this->_resize_image($file['tmp_name'], 200, $file['type']);
			$picture = array('tmp_name' => $min_size, 'name' => $min_size);
			$sResigedPhoto = $this->s3FileUpload($picture);
			$this->parent_model->update_student_profile_photo($input['type'], $input['id'], $sResigedPhoto, 'LOW_QUALITY');
		}
		//if student photo changed? upadte in session data
		if ($input['type'] == 'student' && $input['type'] !='family')
			$this->session->userdata('parentCache')->picture_url = $sResigedPhoto['file_name'];
		//echo $status;
	}

	public function save_profile_data()
	{
		if($_POST['old_value'] != $_POST['field_value'])
		$this->parent_model->store_edit_history($_POST['type'].' '.$_POST['field_name'].' '.$_POST['old_value'],$_POST['type'].' '.$_POST['field_name'].' '.$_POST['field_value']);
		echo $this->parent_model->saveProfileData();
	}

	public function save_address_data()
	{
		$old_data = [];
		$new_data = [];

		if (isset($_POST['old_data']) && isset($_POST['add_data'])) {
			$old_array = $_POST['old_data'];
			$new_array = $_POST['add_data'];
			
			// Compare arrays by keys and check for value changes
			foreach ($new_array as $key => $new_value) {
				if (isset($old_array[$key]) && $old_array[$key] !== $new_value) {
					// Value has changed, add to both arrays
					$old_data[$key] = $old_array[$key];
					$new_data[$key] = $new_value;
				}
			}
			
			// Convert to JSON if needed for storage
			$old_data = !empty($old_data) ? $old_array['type'].' '.$old_array['address_type'].' '.json_encode($old_data) : '';
			$new_data = !empty($new_data) ? $old_array['type'].' '.$old_array['address_type'].' '.json_encode($new_data) : '';
		}
		$this->parent_model->store_edit_history($old_data,$new_data);
		echo $this->parent_model->saveAddressData();
	}

	public function changeAflAcadYear()
	{
		$acad_year_id = $_POST['acad_year_id'];
		$this->session->set_userdata('afl_acad_year_id', $acad_year_id);
		echo 1;
	}

	public function afl()
	{
		$data['acad_year_id'] = $this->acad_year->getAcadYearId();
		if ($this->session->userdata('afl_acad_year_id')) {
			$data['acad_year_id'] = $this->session->userdata('afl_acad_year_id');
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$modules = json_decode($this->settings->getSetting('deactivation_modules'));
		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$data['is_deactivated'] = 0;
		if(!empty($modules)){
			if ($is_deactivated && in_array('AFL', $modules)) {
				$data['is_deactivated'] = 1;
			}
		}
		
		// echo "<pre>"; print_r($data['is_deactivated']); die();
		// $stdData = $this->parent_model->getClassIdOfLoggedInParent();
		$data['class_id'] = $this->parent_model->getYearClassId($studentId, $data['acad_year_id']);
		// $data['subjects'] = $this->parent_model->getAflSubjects($stdData->classId);
		// $data['subjectsAssessments'] = $this->parent_model->getSubjectsAssessments($stdData->classId);
		$months = $this->parent_model->getAssessments($data['class_id']);
		$data['std_id'] = $studentId; //$stdData->stdId;
		// $data['class_id'] = $stdData->classId;
		$data['assessments'] = array();
		$data['current_month'] = '';
		$data['months'] = array();
		if (!empty($months)) {
			// $assessment = $months[0];
			$i = 0;
			foreach ($months as $key => $ass) {
				/*list($month, $year) = explode(" - ", $ass->schedule);
    			$data['months'][] = $month;*/
				$data['months'][] = $ass->schedule;
				$data['assessments'][$ass->schedule] = $ass->id;
				if ($i++ == 0)
					$data['current_month'] = $ass->schedule;
			}
			// $data['assessment'] = $this->parent_model->getAssessmentsSubjects($assessment->id, $stdData->classId, $stdData->stdId);
		}
		$data['acad_years'] = $this->parent_model->getStudentAcadYears($studentId);
		// echo "<pre>"; print_r($data); die();
		$data['main_content'] = 'parent/afl/index';
		$this->load->view('inc/template', $data);
	}

	public function getAssessmentSubjects()
	{
		$std_id = $_POST['std_id'];
		$class_id = $_POST['class_id'];
		$assessment_id = $_POST['assessment_id'];
		$data['subjects'] = $this->parent_model->getAflSubjects($class_id);
		$data['assessment'] = $this->parent_model->getAssessmentsSubjects($assessment_id, $class_id, $std_id);
		echo json_encode($data);
		// echo '<pre>'; print_r($data); die();
	}

	public function performance($assessment_subject_id)
	{
		// echo $assessment_subject_id; die();
		$data['assessment_subject_id'] = $assessment_subject_id;
		// $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$stdData = $this->parent_model->getClassIdOfLoggedInParent();
		// echo '<pre>'; print_r($stdData);
		$data['assessment'] = $this->parent_model->getAssessmentDetailByAssSub($assessment_subject_id);
		// list($month, $)
		// $subject = $this->parent_model->getSubjectDetailByAssSub($assessment_subject_id);
		$data['subject'] = $this->parent_model->getSubjectDetails($assessment_subject_id);
		$this->load->model('afl/afl_grading_model', 'grading_model');
		$this->load->model('afl/afl_assessment_model', 'assessment_model');
		$this->load->model('afl/afl_marks_model', 'marks_model');
		if ($this->mobile_detect->isMobile()) {
			$garde_descriptions = $this->parent_model->getGradeDescriptions($assessment_subject_id);
			$data['marksData'] = $this->parent_model->getStudentMarksData($stdData->stdId, $assessment_subject_id);
			if (empty($data['marksData'])) {
				$data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['assessment']->afl_grading_scale_id);
				$data['parameters'] = $this->assessment_model->getSubjectPerfParameters($assessment_subject_id);
				foreach ($data['parameters'] as $para_id => $parameter) {
					$std = new stdClass();
					$std->self_marks = 'NA';
					$std->perf_parameter_id = $para_id;
					$std->evaluated_marks = 'NA';
					$std->pointer_name = $parameter['pointer_name'];
					$std->description = $parameter['parameter_description'];
					$std->self_range = '';
					$std->self_desc = '';
					$std->evaluated_range = '';
					$std->evaluated_desc = '';
					$data['marksData'][] = $std;
				}
			} else {
				foreach ($data['marksData'] as $key => $marks) {
					$descriptions = $garde_descriptions[$marks->perf_parameter_id];
					$marks->self_range = '';
					$marks->self_desc = '';
					$marks->evaluated_range = '';
					$marks->evaluated_desc = '';
					foreach ($descriptions as $key => $desc) {
						if ($marks->self_marks >= (float)$desc->start_range && $marks->self_marks <= (float)$desc->end_range) {
							$marks->self_desc = $desc->grade_description;
							$marks->self_range = $desc->range_name;
						}
						if ($marks->evaluated_marks >= (float)$desc->start_range && $marks->evaluated_marks <= (float)$desc->end_range) {
							$marks->evaluated_desc = $desc->grade_description;
							$marks->evaluated_range = $desc->range_name;
						}
						if ($marks->self_marks == -2 || $marks->self_marks == -3) {
							$marks->self_marks = 'NA';
						} else {
							$marks->self_marks = (float) $marks->self_marks;
						}
						if ($marks->evaluated_marks == -2 || $marks->evaluated_marks == -3) {
							$marks->evaluated_marks = 'NA';
						} else {
							$marks->evaluated_marks = (float) $marks->evaluated_marks;
						}
					}
				}
			}
		} else {
			$data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['assessment']->afl_grading_scale_id);
			$data['parameters'] = $this->assessment_model->getSubjectPerfParameters($assessment_subject_id);
			$data['marks'] = $this->marks_model->getStudentMarks($assessment_subject_id, $stdData->stdId);
			$data['student'] = $stdData;
			$remarks = $this->marks_model->getStudentRemarks($stdData->stdId, $assessment_subject_id);
			$data['remarks'] = '';
			if (!empty($remarks)) {
				$data['remarks'] = $remarks->remarks;
			}
		}

		$data['school'] = $this->settings->getSetting('school_short_name');
		$data['school_name'] = $this->settings->getSetting('school_name');

		// echo "<pre>"; print_r($data); die();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'parent/afl/subject_wise_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/afl/subject_wise_mobile';
		} else {
			$data['main_content']    = 'parent/afl/subject_wise';
		}

		// $data['main_content'] = 'parent/afl/subject_wise';
		$this->load->view('inc/template', $data);
	}

	public function view_grading_scale($assessment_subject_id)
	{
		$data['assessment_subject_id'] = $assessment_subject_id;
		$data['assessment'] = $this->parent_model->getAssessmentDetailByAssSub($assessment_subject_id);
		$data['subject'] = $this->parent_model->getSubjectDetails($assessment_subject_id);
		$this->load->model('afl/afl_grading_model', 'grading_model');
		$this->load->model('afl/afl_assessment_model', 'assessment_model');
		$this->load->model('afl/afl_marks_model', 'marks_model');
		$data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['assessment']->afl_grading_scale_id);
		$data['parameters'] = $this->assessment_model->getSubjectPerfParameters($assessment_subject_id);
		// echo "<pre>"; print_r($data); die();
		$data['main_content'] = 'parent/afl/grading_scale';
		$this->load->view('inc/template', $data);
	}

	public function cumulative_performance()
	{
		$acad_year_id = $this->acad_year->getAcadYearId();
		if ($this->session->userdata('afl_acad_year_id')) {
			$acad_year_id = $this->session->userdata('afl_acad_year_id');
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$classId = $this->parent_model->getYearClassId($studentId, $acad_year_id);

		// $stdData = $this->parent_model->getClassIdOfLoggedInParent();
		// $subjects = $this->parent_model->getAflSubjects($stdData->classId);
		$subjects = $this->parent_model->getAflApplicableSubjects($classId, $studentId);
		$data['subjects'] = array();
		$data['subject_id'] = (!empty($subjects)) ? $subjects[0]->id : 0;
		$data['subject_name'] = (!empty($subjects)) ? $subjects[0]->subject_name : '';
		foreach ($subjects as $key => $subject) {
			$data['subjects'][] = $subject;
		}
		// echo "<pre>"; print_r($data); die();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'parent/afl/tablet_cumulative_performance';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/afl/mobile_cumulative_performance';
		} else {
			$data['main_content']    = 'parent/afl/cumulative_performance';
		}
		$this->load->view('inc/template', $data);
	}

	public function getSubjectPerformance()
	{
		$subject_id = $_POST['subject_id'];
		$data['subject'] = $this->parent_model->getSubjectName($subject_id);
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$assessments = $this->parent_model->getAflAssessments($subject_id, $student_id);
		$data['grading_scale'] = array();
		if (!empty($assessments))
			$data['grading_scale'] = $this->parent_model->getGradingScale($assessments[0]->afl_grading_scale_id);
		$assessment_subject_ids = array();
		foreach ($assessments as $key => $ass) {
			array_push($assessment_subject_ids, $ass->assessment_subject_id);
		}
		$parameters = $this->parent_model->getSubjectPerfParameters($assessment_subject_ids, $student_id);

		//list of graph line colors to choose from
		// $colors = ['#82b74b', '#3e4444', '#d64161', '#405d27', '#6b5b95', '#feb236', '#ff7b25', '#c1946a'];
		$colors = ['#91caa5', '#ee919f', '#eeeb91', '#c4bceb', '#6b5b95', '#feb236', '#ff7b25', '#c1946a', '#b4b9c0', '#a8a1ce', '#758cff', '#ffc17a'];
		$gData = array();
		$data['xKeys'] = array();
		$data['yKeys'] = array();
		$data['pointer_names'] = array();
		$pointer_names = array();
		$data['colors'] = array();
		$perf_pointers = array();
		$marksArray = array();
		if (!empty($parameters)) {
			foreach ($assessments as $key => $assessment) {
				$i = 1;
				// list($month, $year) = explode(" - ", $assessment->schedule);
				// array_push($data['xKeys'], trim($month));
				array_push($data['xKeys'], trim($assessment->schedule));
				$pMarks = array();
				// $pMarks['month'] = $month;
				$pMarks['month'] = $assessment->schedule;
				foreach ($parameters as $key => $parameter) {
					if (!in_array($parameter['perf_pointer_id'], $perf_pointers)) {
						array_push($perf_pointers, $parameter['perf_pointer_id']);
						array_push($data['yKeys'], 'Parameter' . $i);
						$rand_color = $colors[$i - 1];
						$pointer_names[$parameter['perf_pointer_id']] = array('short_name' => 'Parameter' . $i, 'full_name' => $parameter['pointer_name'], 'color' => $rand_color);
						array_push($data['colors'], $rand_color);
						$i++;
					}

					if ($assessment->assessment_subject_id == $parameter['assessment_subject_id']) {
						$pMarks[$pointer_names[$parameter['perf_pointer_id']]['short_name']] = ($parameter['evaluated_marks'] == -2 || $parameter['evaluated_marks'] == -3) ? null : (float)$parameter['evaluated_marks'];
						$marksArray[$assessment->schedule][$pointer_names[$parameter['perf_pointer_id']]['short_name']] = array(
							'self_marks' => ($parameter['self_marks'] == -2 || $parameter['self_marks'] == -3) ? null : ((float)$parameter['self_marks']),
							'evaluated_marks' => ($parameter['evaluated_marks'] == -2 || $parameter['evaluated_marks'] == -3) ? null : ((float)$parameter['evaluated_marks'])
						);
					}
				}
				$gData[] = $pMarks;
			}
			foreach ($pointer_names as $key => $pName) {
				array_push($data['pointer_names'], $pName);
			}
		}

		$data['graph_data'] = $gData;
		$data['marks_data'] = $marksArray;
		echo json_encode($data);
	}

	public function month_summary($assessment_id)
	{
		$data['assessment_id'] = $assessment_id;
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$this->load->model('afl/afl_assessment_model', 'assessment_model');
		$data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
		$data['grading_scale'] = $this->parent_model->getGradingScale($data['assessment']->afl_grading_scale_id);
		$subjects = $this->parent_model->getMonthSummary($assessment_id, $student_id);
		//list of graph line colors to choose from
		$colors = ['#91caa5', '#ee919f', '#eeeb91', '#c4bceb', '#6b5b95', '#feb236', '#ff7b25', '#c1946a', '#b4b9c0', '#a8a1ce', '#758cff', '#ffc17a'];
		$gData = array();
		$data['yKeys'] = array();
		$data['colors'] = array();
		$data['pointer_names'] = array();
		$pointer_names = array();
		$marksArray = array();
		foreach ($subjects as $subject_id => $subject) {
			$subject_name = $subject['subject_name'];
			$subMarks = array();
			$pointer_names[$subject_name] = array();
			$marksArray[$subject_name] = array();
			$subMarks['subject'] = $subject_name;
			$parameters = $subject['parameters'];
			$i = 1;
			foreach ($parameters as $key => $parameter) {
				$para_name = 'Parameter' . $i;
				$rand_color = $colors[$i - 1];
				if (!in_array($para_name, $data['yKeys'])) {
					array_push($data['yKeys'], $para_name);
					array_push($data['colors'], $rand_color);
				}
				$pointer_names[$subject_name][] = array('short_name' => $para_name, 'full_name' => $parameter->pointer_name, 'color' => $rand_color);
				$subMarks[$para_name] = ($parameter->evaluated_marks == -2 || $parameter->evaluated_marks == -3) ? null : ((float)$parameter->evaluated_marks);
				$marksArray[$subject_name][$para_name] = array(
					'self_marks' => ($parameter->self_marks == -2 || $parameter->self_marks == -3) ? null : ((float)$parameter->self_marks),
					'evaluated_marks' => ($parameter->evaluated_marks == -2 || $parameter->evaluated_marks == -3) ? null : ((float)$parameter->evaluated_marks)
				);
				$i++;
			}
			$gData[] = $subMarks;
		}
		$data['graph_data'] = $gData;
		$data['marks_data'] = $marksArray;
		$data['pointer_names'] = $pointer_names;
		// echo "<pre>"; print_r($data); die();
		$data['main_content'] = 'parent/afl/month_performance';
		$this->load->view('inc/template', $data);
	}

	public function get_full_receipt_pdf()
	{
		$stdSchId = $_POST['stdSchId'];
		$data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_full_receipt($stdSchId);

		$template = $this->fees_collection_model->get_fee_consolidated_template_by_blueprint_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
		if ($template) {
			$result = $this->_create_template_fee_consolidated($data['fee_trans'], $template, 0, 0);
		}
		$update =  $this->fees_collection_model->update_consolidate_html_receipt($result, $stdSchId);
		if ($update) {
			return $this->__generatefee_pdf_consolidated_receipt($result, $stdSchId);
		}
	}

	public function _create_template_fee_consolidated($fee_trans, $template)
	{
		$medium = $this->settings->getSetting('medium')[$fee_trans->student->medium];
		$class = $fee_trans->student->classSection;
		if ($fee_trans->student->is_placeholder == 1) {
			$class = $fee_trans->student->clsName;
		}
		$template = str_replace('%%receipt_no%%', $fee_trans->receipt_number, $template);
		$template = str_replace('%%class%%', $class, $template);
		$template = str_replace('%%class_medium%%', $class . '/' . $medium, $template);
		$template = str_replace('%%transaction_date%%', date('d-m-Y', strtotime($fee_trans->paid_datetime)), $template);
		$template = str_replace('%%f_number%%', $fee_trans->student->mobile_no, $template);
		$template = str_replace('%%student_name%%', $fee_trans->student->stdName, $template);
		$template = str_replace('%%father_name%%', $fee_trans->student->fName, $template);
		$template = str_replace('%%mother_name%%', $fee_trans->student->mName, $template);
		$template = str_replace('%%admission_no%%', $fee_trans->student->admission_no, $template);
		$template = str_replace('%%academic_year%%', $this->acad_year->getAcadYearById($fee_trans->no_of_comp->acad_year_id), $template);
		$i = 1;
		$totalAmount = 0;
		$header_part = '<table>';
		$header_part .= '<tr>';
		$header_part .= '<th width="10%">#</th>';
		$header_part .= '<th>Particulars</th>';
		$header_part .= '<th>Amount</th>';
		$header_part .= '</tr>';
		$component_part = '';
		foreach ($fee_trans->comp as $key => $trans) {
			$totalAmount += $trans->amount_paid + $trans->concession_amount;
			$component_part .= '<tr>';
			$component_part .= '<td>' . $i++ . '</td>';
			$component_part .= '<td>' . $trans->compName . '</td>';
			$component_part .= '<td>' . ($trans->amount_paid + $trans->concession_amount) . '</td>';
			$component_part .= '</tr>';
		}


		$footer_part = '<tr>';
		$footer_part .= '<td colspan="2" style="text-align: right;">Total Fee</td>';
		$footer_part .= '<td>' . $totalAmount . '</td>';
		$footer_part .= '</tr>';

		if ($fee_trans->no_of_ins->total_concession_amount_paid != 0) {
			$footer_part .= '<tr>';
			$footer_part .= '<td colspan="2" style="text-align:right;">Concession (-)</td>';
			$footer_part .= '<td>' . $fee_trans->no_of_ins->total_concession_amount_paid . '</td>';
			$footer_part .= '</tr>';
		}

		if ($fee_trans->no_of_ins->discount != 0) {
			$footer_part .= '<tr>';
			$footer_part .= '<td colspan="2" style="text-align:right;">Discount (-)</td>';
			$footer_part .= '<td>' . $fee_trans->no_of_ins->discount . '</td>';
			$footer_part .= '</tr>';
		}

		if ($fee_trans->no_of_ins->total_fine_amount != 0) {
			$footer_part .= '<tr>';
			$footer_part .= '<td colspan="2" style="text-align:right;">Late Fee</td>';
			$footer_part .= '<td>' . $fee_trans->no_of_ins->total_fine_amount . '</td>';
			$footer_part .= '</tr>';
		}

		if ($fee_trans->no_of_ins->total_card_charge_amount != 0) {
			$footer_partfooter_part .= '<tr>';
			$footer_partfooter_part .= '<td colspan="2" style="text-align:right;">Card Charge Amount</td>';
			$footer_partfooter_part .= '<td>' . $fee_trans->no_of_ins->total_card_charge_amount . '</td>';
			$footer_partfooter_part .= '</tr>';
		}

		$footer_part .= '<tr>';
		$footer_part .= '<td colspan="2" style="text-align:right;border: solid 1px #474747;"><strong>Total</strong></td>';
		$footer_part .= '<td style="border: solid 1px #474747;">' . $fee_trans->no_of_ins->total_fee_paid . '</td>';
		$footer_part .= '</tr>';

		$footer_part .= '</table>';
		$dynamic_part = $header_part . $component_part . $footer_part;

		$amountInWords = $this->getIndianCurrency($fee_trans->no_of_ins->total_fee_paid);

		$template = str_replace('%%installements%%', $dynamic_part, $template);
		$template = str_replace('%%rupees_in_words%%', ucwords($amountInWords), $template);
		return $template;
	}

	private function __generatefee_pdf_consolidated_receipt($html, $stdSchId)
	{
		$school = CONFIG_ENV['main_folder'];
		$path = $school . '/fee_reciepts/' . uniqid() . '-' . time() . ".pdf";

		$bucket = $this->config->item('s3_bucket');

		$status = $this->fees_collection_model->updateConsolidateFeePath($stdSchId, $path);
		$page = 'portrait';
		$page_size = 'a4';
		$curl = curl_init();
		$postData = urlencode($html);
		$username = CONFIG_ENV['job_server_username'];
		$password = CONFIG_ENV['job_server_password'];
		$return_url = site_url() . 'Callback_Controller/updateConsolidatedFeePdfLink';

		curl_setopt_array($curl, array(
			CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_USERPWD => $username . ":" . $password,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "POST",
			CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&page=" . $page . "&page_size=" . $page_size . "&data=" . $postData . "&return_url=" . $return_url,
			CURLOPT_HTTPHEADER => array(
				"Accept: application/json",
				"Cache-Control: no-cache",
				"Content-Type: application/x-www-form-urlencoded",
				"Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
			),
		));

		$response = curl_exec($curl);
		$err = curl_error($curl);
		curl_close($curl);
	}


	public function consolidated_receipt_pdf_download($stdSchId)
	{
		$link = $this->fees_collection_model->download_consolidated_fee_receipt($stdSchId);
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download('fee_reciept.pdf', $data, TRUE);
	}

	public function event()
	{
		
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();

		$data['events'] = $this->parent_model->get_event_registrations_details($studentId);

		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
        $modules = json_decode($this->settings->getSetting('deactivation_modules'));
        $data['is_deactivated'] = 0;
        if (!empty($modules)) {
           if($is_deactivated && in_array('Events', $modules)) {
                $data['is_deactivated'] = 1;
            }
        }
		$data['main_content'] = 'parent/event/index';
		$this->load->view('inc/template', $data);
	}

	public function register_event($event_id='')
	{
		
		$data['event_id'] = $event_id;
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['event'] = $this->parent_model->get_register_event_detailsbyId($event_id);
		if (empty($data['event'])) {
			redirect('parent_controller/event');
		}
		$sub_event = $this->parent_model->get_sub_events_details($event_id);

		foreach ($sub_event as $key => $val) {
			$val->regCount = 0;
			if (array_key_exists($val->id, $data['event']->subRegister)) {
				$val->regCount =  $data['event']->subRegister[$val->id];
			}
		}
		$data['sub_event'] = $sub_event;
		$data['wallet_balance'] = $this->parent_model->get_wallet_balance_bystudentid($studentId);
		$data['main_content'] = 'parent/event/register_index';
		$this->load->view('inc/template', $data);
	}

	public function get_register_event_details()
	{
		$event_id = $_POST['event_id'];
		$result = $this->parent_model->get_register_event_detailsbyId($event_id);
		echo json_encode($result);
	}

	public function view_past_events()
	{
		$data['main_content'] = 'parent/event/event_details_demo';
		$this->load->view('inc/template', $data);
	}

	public function insert_event_registration()
	{
		$event_id = $_POST['event_id'];
		$regAmount = $_POST['regAmount'];
		$safetyAmount = $_POST['safetyAmount'];
		$sub_event_id = $_POST['sub_event_id'];
		echo $this->parent_model->insert_event_registrationbyparent($event_id, $regAmount, $safetyAmount, $sub_event_id);
	}

	public function register_event_view($eventId)
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['event'] = $this->parent_model->get_success_event_detailsbyId($eventId, $studentId);
		$data['wallet_balance'] = $this->parent_model->get_wallet_balance_bystudentid($studentId);
		$data['main_content'] = 'parent/event/success_event';
		$this->load->view('inc/template', $data);
	}
	public function success_registration_event($eventId)
	{
		$data['event'] = $this->parent_model->get_success_event_detailsbyId($eventId);
		$data['main_content'] = 'parent/event/success_event';
		$this->load->view('inc/template', $data);
	}

	public function event_details($eventId = '')
	{
		$data['event'] = $this->parent_model->get_event_detailsbyeventId($eventId);
		$data['main_content'] = 'parent/event/event_details';
		$this->load->view('inc/template', $data);
	}

	public function download_event_file($eventId)
	{
		$link = $this->parent_model->downloadEventAttachment($eventId);
		$url = $this->filemanager->getFilePath($link);
		$names = explode("/", $url);
		$filename = $names[count($names) - 1];
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($filename, $data, TRUE);
	}

	public function get_terms_condition_blueprintwise()
	{
		$blueprintId = $_POST['blueprintId'];
		echo $this->parent_model->get_terms_condition_blueprintwise_data($blueprintId);
	}

	public function other_links()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$is_student_partially_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$deactivated_modules = json_decode($this->settings->getSetting('deactivation_modules'));

		$is_deactivated = 0;
		if(!empty($deactivated_modules)){
			if ($is_student_partially_deactivated && in_array('Links', $deactivated_modules)) {
				$is_deactivated = 1;
			} else {
				$is_deactivated = 0;
			}
		}
	

		if ($is_deactivated == 1) {
			$data["module_name"] = "Diya Links";
			$data['main_content'] = 'parent/temporary_deactive_page.php';
		}else{
			$this->load->model('Otherlinks_model');
			$data['links'] = $this->Otherlinks_model->getEnabledLinksForParents();
			$data['main_content'] = 'other_links/index';
		}
		$this->load->view('inc/template', $data);
	}

	public function help_support()
	{
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'helptext/about_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'helptext/about_mobile';
		} else {
			$data['main_content']    = 'helptext/about_desktop';
		}

		$this->load->view('inc/template', $data);
	}

	public function viewer()
	{
		$data['back_url'] = $_POST['back_url'];
		$data['page_url'] = $_POST['page_url'];
		$data['main_content']    = 'parent/viewer.php';
		$this->load->view('inc/template_virtualClass', $data);
	}

	public function roboHelp_display()
	{
		$data['back_url'] = $_POST['back_url'];
		$data['page_url'] = $_POST['page_url'];
		//echo "<pre>"; print_r($data); die();
		if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'helptext/parent/mobile_index.php';
		} else {
			$data['main_content']    = 'helptext/parent/desktop_index.php';
		}
		$this->load->view('inc/template_virtualClass', $data);
	}

	public function notification_test()
	{
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']    = 'helptext/notification_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'helptext/notification_mobile';
		} else {
			$data['main_content']    = 'helptext/notification_desktop';
		}
		$this->load->view('inc/template', $data);
	}

	public function submit_notification()
	{
		$this->load->helper('notification_helper');
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$avatar_id = $this->authorization->getAvatarId();

		$stake_holder_id = $this->db->select('a.stakeholder_id')
			->from('avatar a')
			->where('a.id', $avatar_id)
			->get()->row();
		$parent_type = $this->db->select('sr.relation_type')
			->from('student_relation sr')
			->where('sr.id', $stake_holder_id->stakeholder_id)
			->get()->row();

		$title = $this->settings->getSetting('school_name');
		$message = 'Notification';
		$url = site_url('parent_controller/notification_test');
		$res = sendStudentNotifications([$student_id], $title, $message, $url, $parent_type->relation_type);

		// 	{
		// 		"request":[
		// 			 {
		// 					"user_id":"2291",
		// 					"token":"eTEf-h5Vw88:APA91bGrOLqyPT31MbZ6Y9nHhzYbhikLuBWmcpuwlu6KqwU2Q8kzFyIjWbXUnxfP095fjUZcFVxbyltSOmEhMHLygekBKm7wrxPHUd76_3doFD6KednY_0IPqlYV38ZCwm6Dhed63Acs"
		// 			 }
		// 		],
		// 		"response":{
		// 			 "status":1,
		// 			 "message":"{\"multicast_id\":4123390631487021825,\"success\":1,\"failure\":0,\"canonical_ids\":0,\"results\":[{\"message_id\":\"0:1596002293129526%033b320b033b320b\"}]}"
		// 		}
		//  }
		$response = $res['response'];
		// trigger_error(json_encode($res));
		$data['status'] = '';
		if ($response['status'] == 1) {
			$result = json_decode($response['message']);
			if ($result->success == 1) {
				$data['status'] = 'Successfulyy sent';
				$this->session->set_flashdata('flashSuccess', 'Notification sent successfully ');
			} else {
				$data['status'] = 'Failed';
				$this->session->set_flashdata('flashError', 'Sending notification failed');
				// redirect('parent_controller/notification_fail');
			}
		} else {
			$data['status'] = 'Failed';
			$this->session->set_flashdata('flashError', 'Sending notification failed');
			// redirect('parent_controller/notification_fail');

		}
		redirect('parent_controller/notification_test');
	}

	public function notification_fail()
	{
		$data['main_content']    = 'helptext/notification_fail';
		$this->load->view('inc/template', $data);
	}

	public function lp_task_multiple_files_crud($task_id)
	{
		$lesson_config = $this->settings->getSetting('lesson_plan');
		if ($lesson_config) {
			$data['lp_task_max_submit_file'] = $lesson_config->lp_task_max_submit_file;
			$data['size'] = $lesson_config->size;
		} else {
			$data['lp_task_max_submit_file'] = "2";
			$data['size'] = "5MB";
		}
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		// $data['aws'] = $this->get_chunk_upload_secret();
		// $this->load->library('aws_library');
		// $data['aws'] = $this->aws_library->getSignatureData();
		// $data['task'] = $this->parent_model->getSingleStudentTask($task_id,$studentId);
		$data['task'] = $this->parent_model->getStudentTaskMinimumInfo($task_id, $studentId);
		if ($data['task']->submission_status == 1) {
			redirect('parent_controller/view_student_task/' . $task_id);
		}
		$data['task_id'] = $task_id;
		// echo "<pre>";print_r($data);die();
		// $data['main_content'] = 'parent/student_tasks/mobile_multiple_file_view';
		// $data['main_content'] = 'parent/student_tasks/mobile_task_submit';
		$data['main_content'] = 'parent/student_tasks/mobile_task_submit_frontend';
		$this->load->view('inc/template', $data);
	}

	public function student_reward()
	{
		if (!$this->settings->isParentModuleEnabled('STUDENT_REWARD')) {
			redirect('dashboard', 'refresh');
		}

		$data['main_content'] = 'parent/student_reward/reward_report';
		$this->load->view('inc/template', $data);
	}

	public function get_student_reward()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_id'] = $studentId;
		// echo "<pre>";print_r($studentId);die();
		$data['getStudents'] = $this->parent_model->getStudentDetails($studentId);
		echo json_encode($data);
	}
	public function getAssessmentQuestions()
	{
		$task_id = $_POST['task_id'];
		$assessment_id = $_POST['assessment_id'];
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data = $this->parent_model->getAssessmentQuestions($task_id, $assessment_id, $studentId);
		echo json_encode($data);
	}

	public function submitAssessment()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$status = $this->parent_model->submitAssessment($studentId);
		echo $status;
	}

	public function update_profile_confirmed()
	{
		$this->parent_model->store_edit_history('','Profile Confirmed');
		$result = $this->parent_model->update_profile_confirmedbyid($_POST['stdYearId']);
		echo json_encode($result);
	}

	public function certificates()
	{

		$data['student_uid'] = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_uid']);
		// echo $data['student_uid']; die();
		// echo $studentId; die();
		$data['certificates'] = $this->Certificates_Model->get_certificates_parent($data['student_uid']);
		// echo "<pre>"; print_r($data); die();
		if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/show_certificates_mobile';
		} else {
			$data['main_content'] = 'parent/show_certificates';
		}

		$this->load->view('inc/template', $data);
	}

	public function pdf_certificate_download($certificate_id, $template_name, $student_name)
	{
		$link = $this->Certificates_Model->getPDFLink($certificate_id);
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$cert_name = $template_name . ' of ' . $student_name . '.pdf';
		// echo "data".$data; die();
		$this->load->helper('download');
		force_download($cert_name, $data, TRUE);
	}

	public function accept_terms_condition_blueprintwise()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$cohort_student_id = $_POST['cohort_student_id'];
		$result = $this->parent_model->update_tnc_status($cohort_student_id);
		$email_content = $this->parent_model->get_parent_email($studentId,'consent_form_email_confirmation_in_fees');
		
		if(!empty($email_content)){
			$mail_sent = $this->_email_to_parent($email_content);
			
			if($mail_sent){
				$sender_list = [];
				$sender_list['students'] = [[
					'send_to' => 'Both',
					'send_to_type' => 'Father',
					'ids' => $studentId,
				],
				[
					'send_to' => 'Both',
					'send_to_type' => 'Mother',
					'ids' => $studentId,
				]];
				
				$email_master_data = array(
				'subject' => $email_content['email_subject'],
				'body' => $email_content['content'],
				'source' => 'Fee Invoice',
				'sent_by' => $this->authorization->getAvatarId(),
				'recievers' => 'Parents',
				'from_email' => $email_content['registered_email'],
				'files' => null,
				'acad_year_id' => $this->acad_year->getAcadYearId(),
				'visible' => 1,
				'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
				'sending_status' => 'Completed',
				'texting_master_id'=>$email_content['id']
				);
				$this->load->model('communication/emails_model');
				$email_master_id = $this->emails_model->saveEmail($email_master_data);
			}
		}
		echo $result;
	}

	private function _fee_email_to_parent($input, $fee_details = []){
		$this->load->helper('email_helper');

		$emailIds = $input['to_emails'];
		$memberEmail = [];
		
		foreach ($emailIds as $key => $val) {
			array_push($memberEmail, $val);
		}

		$input['template_content'] = str_replace('%%father_name%%',$input['to_email']->father_name,$input['template_content']);
		$input['template_content'] = str_replace('%%mother_name%%',$input['to_email']->mother_name,$input['template_content']);
		$input['template_content'] = str_replace('%%student_name%%',$input['to_email']->student_name,$input['template_content']);
		$input['template_content'] = str_replace('%%student_grade%%',$input['to_email']->grade,$input['template_content']);

		if(!empty($fee_details)){
			$dateTimeObject = new DateTime($fee_details['paid_datetime']);
			$input['email_subject'] = str_replace('%%std_name%%',$input['to_email']->student_name,$input['email_subject']);
			$input['template_content'] = str_replace('%%std_name%%',$input['to_email']->student_name,$input['template_content']);
			$input['template_content'] = str_replace('%%grade_sec%%',$input['to_email']->grade,$input['template_content']);
			$input['template_content'] = str_replace('%%fee_paid_amount%%',$fee_details['paid_amount'],$input['template_content']);
			$input['template_content'] = str_replace('%%payment_date%%',$dateTimeObject->format('d-M-Y h:i A'),$input['template_content']);
			$input['template_content'] = str_replace('%%payment_method%%',$fee_details['transaction_mode'],$input['template_content']);
			if($input['logged_in_as'] == 'Father'){
				$input['template_content'] = str_replace('%%parent_name%%',$input['to_email']->father_name,$input['template_content']);
			} else {
				$input['template_content'] = str_replace('%%parent_name%%',$input['to_email']->mother_name,$input['template_content']);
			}
		}

		$boards = $this->settings->getSetting('board');
		$board = '';
		foreach($boards as $key => $val){
			if($key == $input['to_email']->board){
				$board = $val;
			}
		}
		$input['template_content'] = str_replace('%%curriculum_interested%%',$board,$input['template_content']);
		return array('template_content'=>$input['template_content'], 'email_subject'=>$input['email_subject'], 'memberEmail'=>$memberEmail,'registered_email'=>$input['registered_email']);
		// return sendEmail($input['template_content'], $input['email_subject'], 0, $memberEmail, $input['registered_email'], '');
	}
	private function _email_to_parent($input, $fee_details = []){
		$this->load->helper('email_helper');

		$emailIds = $input['to_emails'];
		$memberEmail = [];
		
		foreach ($emailIds as $key => $val) {
			array_push($memberEmail, $val);
		}

		$input['template_content'] = str_replace('%%father_name%%',$input['to_email']->father_name,$input['template_content']);
		$input['template_content'] = str_replace('%%mother_name%%',$input['to_email']->mother_name,$input['template_content']);
		$input['template_content'] = str_replace('%%student_name%%',$input['to_email']->student_name,$input['template_content']);
		$input['template_content'] = str_replace('%%student_grade%%',$input['to_email']->grade,$input['template_content']);

		if(!empty($fee_details)){
			$dateTimeObject = new DateTime($fee_details['paid_datetime']);
			$input['email_subject'] = str_replace('%%std_name%%',$input['to_email']->student_name,$input['email_subject']);
			$input['template_content'] = str_replace('%%std_name%%',$input['to_email']->student_name,$input['template_content']);
			$input['template_content'] = str_replace('%%grade_sec%%',$input['to_email']->grade,$input['template_content']);
			$input['template_content'] = str_replace('%%fee_paid_amount%%',$fee_details['paid_amount'],$input['template_content']);
			$input['template_content'] = str_replace('%%payment_date%%',$dateTimeObject->format('d-M-Y h:i A'),$input['template_content']);
			$input['template_content'] = str_replace('%%payment_method%%',$fee_details['transaction_mode'],$input['template_content']);
			if($input['logged_in_as'] == 'Father'){
				$input['template_content'] = str_replace('%%parent_name%%',$input['to_email']->father_name,$input['template_content']);
			} else {
				$input['template_content'] = str_replace('%%parent_name%%',$input['to_email']->mother_name,$input['template_content']);
			}
		}

		$boards = $this->settings->getSetting('board');
		$board = '';
		foreach($boards as $key => $val){
			if($key == $input['to_email']->board){
				$board = $val;
			}
		}
		$input['template_content'] = str_replace('%%curriculum_interested%%',$board,$input['template_content']);
		return sendEmail($input['template_content'], $input['email_subject'], 0, $memberEmail, $input['registered_email'], '');
	  }

	public function update_is_read_on_desktop_by_parent_id()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$parentId = $this->authorization->getAvatarStakeHolderId();
		echo $this->parent_model->update_read_by_parent_id($parentId);
	}

	public function wallet($acad_year=null)
	{
		$toggle_acad_year=0;
		if(! empty($this->settings->getSetting("disable_acad_year_student_wallet_parent"))){
			$toggle_acad_year=$this->settings->getSetting("disable_acad_year_student_wallet_parent");
		}
		$data['toggle_acad_year']=$toggle_acad_year;

		if($acad_year === null && $toggle_acad_year==0){
			$acad_year = $this->yearId;
		}
		$data['academic_year']=$acad_year;
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['wallet'] = $this->student_wallet_model->get_student_wallet_history_by_id($studentId,$acad_year);
		$data['new_wallet_balance'] = $this->student_wallet_model->get_student_wallet_balance($studentId,$acad_year);
		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$total_wallet_amount=0;
		foreach ($data['wallet']['tx_data'] as  $value) {
			if($value->transaction_type=='Debit'){
				$total_wallet_amount+=$value->amount;
			}
		}
		$data['total_wallet_amount']=$total_wallet_amount; // transaction amount

		$total_amount=0;
		foreach ($data['wallet']['tx_data'] as  $value) {
			if($value->transaction_type=='Load'){
				$total_amount+=$value->amount;
			}
		}
		$data['total_amount']=$total_amount; // total amount

        $modules = json_decode($this->settings->getSetting('deactivation_modules'));
        $data['is_deactivated'] = 0;
        if (!empty($modules)) {
           if($is_deactivated && in_array('Wallet', $modules)) {
                $data['is_deactivated'] = 1;
            }
        }
		$data['acadList'] = $this->student_wallet_model->getAcadYearListForParents();
		$data['main_content'] = 'parent/wallet/index';
		$this->load->view('inc/template', $data);
	}

	public function vaccination_status()
	{
		$data['main_content'] = 'parent/vaccination/index';
		$this->load->view('inc/template', $data);
	}

	public function get_parent_vaccination_details()
	{
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$result = $this->parent_model->get_certificates_details($studentId);
		echo json_encode($result);
	}

	public function save_vaccination_files()
	{
		$parent_id = $_POST['parent_id'];
		$path = $_POST['paths'];
		$vaccine_name = $_POST['vaccine_name'];
		$vaccine_status = $_POST['vaccine_status'];
		$relation_type = $_POST['relation_type'];
		echo $this->parent_model->save_vaccination_details_by_id($parent_id, $path, $vaccine_name, $vaccine_status, $relation_type);
	}

	public function download_certificate($parent_id, $relation_type)
	{
		$link = $this->parent_model->download_vaccination_certificate_by_id($parent_id, $relation_type);
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download('vaccination_certificat.pdf', $data, TRUE);
	}

	public function change_installments()
	{
		$input = $this->input->post();
		$data['cohort_student_id'] = $input['change_ui_cohort_student_id'];
		$data['installments'] = $this->parent_model->get_multiple_installment_details($data['cohort_student_id']);
		$data['blueprint_id'] = $input['blueprint_id'];
		$data['student_id'] = $input['student_id'];
		$data['std_sch_id'] = $input['std_sch_id'];
		$data['blueprint_name'] = $input['blueprint_name'];
		$data['online_status'] = $input['online_payment'];

		if ($this->mobile_detect->isTablet()) {
			$data['main_content']    = 'parent/feesv2/change_installment_tablet';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/feesv2/change_installment_mobile';
		} else {
			$data['main_content']    = 'parent/feesv2/change_installment';
		}
		$this->load->view('inc/template', $data);
	}

	public function get_installment_details()
	{
		$cohort_id = $_POST['cohort_id'];
		$result = $this->parent_model->get_installment_details_by_cohort_id($cohort_id);
		echo json_encode($result);
	}

	public function insert_student_cohort_details()
	{
		$cohort_id = $_POST['cohort_id'];
		$blueprint_id = $_POST['blueprint_id'];
		$cohort_student_id = $_POST['cohort_student_id'];
		$stdId = $this->parent_model->getStudentIdOfLoggedInParent();
		$this->db->trans_begin();

		$auditDesc = 'Reset Fee Structure';
		$this->fees_student_model->insert_fee_audit($stdId, 'Reset Fee Structure', $auditDesc, $this->authorization->getAvatarId(), $blueprint_id);
		$this->fees_collection_model->reset_confirm_student_cohort_data($cohort_student_id);

		$fee_amount = $this->_get_cohort_fee_amount_details($blueprint_id, $cohort_id);
		$input = array();
		foreach ($fee_amount['insData'] as $key => $value) {
			foreach ($value as $key => $val) {
				$input['blueprint_installment_type_id'] = $val->feev2_blueprint_installment_types_id;
				$input['comp_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = $val->compAmount;
				$input['concession_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = 0;
				$input['concession_name'] = '';
			}
		}

		foreach ($fee_amount['fine_amount'] as $insName => $fine_value) {
			foreach ($fine_value as $insId => $val) {
				$input['fine_amount'][$insId] = $val['fine_amount'];
			}
		}

		$rdata = $this->fees_student_model->insert_cohort_details($blueprint_id, $cohort_id, 'STANDARD', $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $stdId, $input['concession_name'], $input['fine_amount'], 0);
		if (empty($rdata)) {
			echo 0;
		} else {
			$this->parent_model->publish_fees_cohort_student_details($rdata['cohort_student_id']);
			if ($this->db->trans_status()) {
				$this->db->trans_commit();
				echo json_encode($rdata);
			}
		}
	}

	private function _get_cohort_fee_amount_details($blueprintId, $cohort_id)
	{
		return $this->fees_collection_model->student_assign_fee_cohort_component_structure($cohort_id);
	}

	public function fee_detailed_transaction()
	{
		$data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['acad_year_selection'] = $this->parent_model->get_fee_total_acad_year($data['student_id']);

		$data['allacadyears'] = $this->acad_year->getAllYearData();
		$data['currentAcadyearId'] = $this->acad_year->getAcadYearId();

		// echo "<pre>"; print_r($data['details_fee_report']); die();
		$data['main_content'] = 'parent/feesv2/transaction_detailed_index';
		$this->load->view('inc/template', $data);
	}

	public function fee_detailed_summary()
	{
		$stdId = $_POST['stdId'];
		$acadyearId = $_POST['acadyearId'];
		$result = $this->parent_model->get_fee_detailed_transaction($stdId, $acadyearId);
		echo json_encode($result);
	}
	public function get_student_fee_summary_details()
	{
		$stdId = $_POST['stdId'];
		$acadyearId = $_POST['acadyearId'];
		$result = $this->parent_model->get_student_fee_summary_details_by_id($stdId, $acadyearId);
		echo json_encode($result);
	}

	public function diary()
	{
		$data["student_id"] = $this->parent_model->getStudentIdOfLoggedInParent();
		$data["parentId"] = $this->authorization->getAvatarStakeHolderId();
		$data['main_content'] = 'student/digital_diary/index';
		$this->load->view('inc/template', $data);
	}

	public function get_dairy_text_by_date(){
		$student_id=$_POST["student_id"];
		$parentId=$_POST["parent_id"];
		$date=$_POST["date"];
		$text=$this->parent_model->get_dairy_text_by_date($student_id, $parentId, $date);
		$circular=$this->parent_model->get_dairy_circular_by_date($student_id, $parentId, $date);
		$calender = $this->parent_model->get_dairy_calendar_by_date($student_id, $parentId, $date);
		$student_task = $this->parent_model->get_dairy_student_task_by_date($student_id, $parentId, $date);
		echo json_encode(array('text'=>$text,'circular'=>$circular,'calender'=>$calender,'student_task'=>$student_task));
	}



	public function classroom_chroniclesview(){
	    $data['main_content']    = 'classroom_chronicles/parentside_view_menu';
	    $this->load->view('inc/template', $data);
  	}
   public function get_chronicles_report_parent(){
   	
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
    $result = $this->Classroom_chronicles_model->getChroniclesReport($from_date, $to_date, $student_id);
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);
  }

  public function get_chronicles_by_id(){
    $id = $_POST['id'];
    $data = $this->Classroom_chronicles_model->getchronicles_id($id);
    echo json_encode($data);
  }

  public function submit_aadhar_details(){
	
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$result = $this->parent_model->submit_aadhar_details($_POST,$studentId,$this->s3FileUpload($_FILES['aadhar_document'], 'Aadhar'));
	if ($result) {
		$this->session->set_flashdata('flashSuccess', 'Submitted');
	  } else {
		$this->session->set_flashdata('flashError', 'Something Went Wrong');
	  }
	echo $result;
  }

  public function submit_pancard_details(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$result = $this->parent_model->submit_pancard_details($_POST,$studentId,$this->s3FileUpload($_FILES['pan_card_document'], 'PANCard'));
	if ($result) {
		$this->session->set_flashdata('flashSuccess', 'Submitted');
	  } else {
		$this->session->set_flashdata('flashError', 'Something Went Wrong');
	  }
	echo $result;
  }

  public function get_aadhar_details(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$result = $this->parent_model->get_aadhar_details($_POST,$studentId);
	echo json_encode($result);
  }

  public function get_pan_details(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$result = $this->parent_model->get_pan_details($_POST,$studentId);
	echo json_encode($result);
  }
  
	public function create_authorization_with_picture(){
		$parent_id = $this->authorization->getAvatarStakeHolderId();
		$data['parent_id']=$parent_id;
		$logged_in_parent_name=$this->student_analytics_model->get_logged_in_parent_name($parent_id);
		$data['name']=$logged_in_parent_name->first_name;
		if($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'parent/escort_auth/create/index_for_picture';
		} else {
			$data['main_content']    = 'parent/escort_auth/create/index_for_picture_desktop';
		}
		$this->load->view('inc/template', $data);
	}

	public function add_created_auth_data_with_picture(){
		$result = $this->student_analytics_model->add_created_auth_data_with_picture();
		echo json_encode($result);
	}

  public function store_document_details(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$document_path = '';
      if(isset($_FILES['document_file'])){
        $document_path = $this->s3FileUploadAsPrivate($_FILES['document_file'],'document');
      }
      $ackn_path = '';
      if(isset($_FILES['acknowledgement_file'])){
        $ackn_path = $this->s3FileUploadAsPrivate($_FILES['acknowledgement_file'],'document');
      }
      $declaration_path = '';
      if(isset($_FILES['declaration_file'])){
        $declaration_path = $this->s3FileUploadAsPrivate($_FILES['declaration_file'],'document');
      }
	$result = $this->parent_model->store_document_details($_POST,$studentId,$document_path,$ackn_path,$declaration_path);
	if ($result) {
		$this->session->set_flashdata('flashSuccess', 'Submitted');
	  } else {
		$this->session->set_flashdata('flashError', 'Something Went Wrong');
	  }
	echo $result;
  }

  public function get_student_documents(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$result = $this->parent_model->get_student_documents($studentId);
	echo json_encode($result);die();
  }

  

  public function get_student_data_tab_wise(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$result = $this->parent_model->get_student_data_tab_wise($studentId);
	$data['display_fields'] = (array)$this->settings->getSetting('student_profile_display_columns');
	if(empty($data['display_fields']['personal_info'])){
		$data['display_fields']['personal_info'] = array();
	}
	if(empty($data['display_fields']['parent_info'])){
		$data['display_fields']['parent_info'] = array();
	}
	if(empty($data['display_fields']['guardian_info'])){
		$data['display_fields']['guardian_info'] = array();
	}
	if(empty($data['display_fields']['driver_info'])){
		$data['display_fields']['driver_info'] = array();
	}
	$chunkfield = array_chunk($data['display_fields']['personal_info'], 2);
    $fatherfield = array_chunk($data['display_fields']['parent_info'], 2);
    $guardianfield = array_chunk($data['display_fields']['guardian_info'], 2);
	$driverfield = array_chunk($data['display_fields']['driver_info'], 2);
	// echo '<pre>';print_r($chunkfield);die();
	echo json_encode(array('student_data'=>$result, 'enabled_field'=>$chunkfield, 'f_enabled_field'=>$fatherfield, 'g_enabled_field'=>$guardianfield, 'd_enabled_field'=>$driverfield));

  }

  public function get_student_school_details(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$result = $this->parent_model->get_student_school_details($studentId);
	$data['display_fields'] = (array)$this->settings->getSetting('student_profile_display_columns');
	if(empty($data['display_fields']['school_info'])){
		$data['display_fields']['school_info'] = array();
	}
	$schoolfield = array_chunk($data['display_fields']['school_info'], 2);
	echo json_encode(array('student_data'=>$result, 'enabled_field'=>$schoolfield));
  }

  public function get_studentDocuments(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$result = $this->parent_model->get_studentDocuments($studentId);
	echo json_encode($result);
  }

  public function get_prev_schooling_details() {
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
    $result = $this->parent_model->get_prev_schooling_details($studentId);

    echo json_encode($result);
  }

  public function fee_summary() {
    $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
    //$stdId = $_POST['stdId'];
    //$acadyearId = $_POST['acadyearId'];
    $data['academic_year'] = $this->acad_year->getAcadYearId();
    $result = $this->parent_model->get_fee_detailed_transaction($student_id, $data['academic_year']);
    echo json_encode($result);

  }

  public function download_fees_recipt_by_rowid(){
    $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
    $data['excess_amount'] = $this->fees_collection_model->get_over_all_additional_amount($student_id);
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');

    $data['fee_data'] = $this->reports_model->fee_student_history_data_by_stdbyId($student_id);

    $fee_blueprints = $this->fees_student_model->get_blueprintsv1();
    // $acad_year_id = $this->acad_year->getAcadYearId();
    foreach ($fee_blueprints as &$fbp) {
      //Assign cohorts if not assigned yet
      // $acad_year_id = $fbp->acad_year_id;
      $fbp->std_fee = $this->fees_collection_model->fee_student_fee_details($student_id, $fbp->id);
      $fbp->compCount = $this->fees_collection_model->fee_total_no_of_components($fbp->id);
      $fbp->concession_adjustment = $this->fees_student_model->get_concession_adjustment_amount($student_id, $fbp->id);
      if (!empty($fbp->std_fee)) {
        $fbp->installments = $this->fees_collection_model->get_installments_all_history($fbp->std_fee->feev2_blueprint_installment_types_id);
        $fbp->history = $this->fees_collection_model->fee_student_fee_history($fbp->std_fee->stdSchId);
      }
    }
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['fee_refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['delete_authorization'] = $this->authorization->isAuthorized('FEESV2.SOFT_DELETE_RECEIPTS');
    // $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id, $acad_year_id);
    $data['fee_history'] = $fee_blueprints;
    echo json_encode($data);
  } 

  public function get_subject_wise_attendance() {
    $from_date =$_POST['from_date'];
    $to_date =$_POST['to_date'];
    $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
    $result =$this->parent_model->get_subject_wise_attendance($from_date,$to_date,$student_id);
    echo json_encode($result);
  }

  public function get_circular()
  {
    $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
    $result = $this->parent_model->get_student_circular($student_id);
    echo json_encode($result);
  }

  public function get_transposrtation_details()
  {
    $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
    $result = $this->parent_model->get_transposrtation_detailset($student_id);
    echo json_encode($result);
  }

  public function get_student_health_details()
  {
    $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
    $disabled_fields = $this->parent_model->get_health_disabled_fields();
    $result=$this->parent_model->whole_medical_history_360($student_id);
    // $healthfields = array_chunk($result, 2);
    // echo "<pre>"; print_r($disabled_fields);
    // echo "<pre>"; print_r($result);
    //  die();
    echo json_encode(array('result'=>$result, 'enabled'=>$disabled_fields));
  }
  public function give_authorization(){
	$student_id= $this->parent_model->getStudentIdOfLoggedInParent();
	$data['todays_requests']= $this->parent_model->get_todays_requests($student_id);

	// echo "<pre>"; print_r($data['todays_requests']);
    // die();

	$data['main_content']    = 'parent/escort_auth/give_authorization';
	$this->load->view('inc/template', $data);
}

public function approve() {
	$result = $this->parent_model->approve();
    echo json_encode($result);
}

public function reject() {
	$result = $this->parent_model->reject();
    echo json_encode($result);
}

private function _staff_notification_parent_fees($staffIds, $blueprintName, $studentName, $classSection, $feeAmount){
	$this->load->helper('texting_helper');
	$input_arr = array();
	$input_arr['staff_ids'] = $staffIds;
	$input_arr['mode'] = 'notification';
	$input_arr['source'] = 'Staff Notification Fee Payment';
	$input_arr['send_to'] = 'Both';
	$input_arr['message'] = $studentName.'('.$classSection.')' .$blueprintName. ' Rs.'.$feeAmount. 'Paid SuccessFully.';
	$response = sendText($input_arr);
	if ($response['success'] != '') {
		$status = 1;
	} else {
		$status = 0;
	}
	return $status;
}

private function _parent_notification_paid_fees($student_ids, $blueprintName, $studentName, $classSection, $feeAmount, $transaction_id){
	$message = $this->settings->getSetting('parent_fee_payment_notifcation');
	if(!empty($message)){
		$message = str_replace('%%amount%%', $feeAmount, $message);
		$message = str_replace('%%trans_id%%', $transaction_id, $message);
		$message = str_replace('%%classSection%%', $classSection, $message);
		$message = str_replace('%%studentName%%', $studentName, $message);
		$message = str_replace('%%blueprintName%%', $blueprintName, $message);
	}
	
	$this->load->helper('texting_helper');
	$input_arr = array();
	$input_arr['student_ids'] = $student_ids;
	$input_arr['mode'] = 'notification';
	$input_arr['source'] = 'Parent Notification Fee Payment';
	$input_arr['send_to'] = 'Both';
	if(!empty($message)){
		$input_arr['message'] = $message;
	}else{
		$input_arr['message'] = $studentName.'('.$classSection.')' .$blueprintName. ' Rs.'.$feeAmount. 'Paid SuccessFully.';
	}
	$response = sendText($input_arr);
	if ($response['success'] != '') {
		$status = 1;
	} else {
		$status = 0;
	}
	return $status;
}

public function download_declaration($file_name,$type){
	if($type == "aadhar"){
		$declaration_path = $this->settings->getSetting('adhar_declaration_path') ;
	}else if($type == 'pan'){
		$declaration_path = $this->settings->getSetting('pan_card_declaration_path') ;
	}
	$document_data = file_get_contents($declaration_path);
	$this->load->helper('download');
	force_download($file_name.'.pdf', $document_data, TRUE);
	
}
public function student_exit_flow(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$is_student_partially_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
	$deactivated_modules = json_decode($this->settings->getSetting('deactivation_modules'));

	$is_deactivated = 0;
	if(!empty($deactivated_modules)){
		if ($is_student_partially_deactivated && in_array('Apply_TC', $deactivated_modules)) {
			$is_deactivated = 1;
		} else {
			$is_deactivated = 0;
		}
	}
	

	if ($is_deactivated == 1) {
		$data["module_name"] = "Apply for TC";
		$data['main_content'] = 'parent/temporary_deactive_page.php';
	} else {
		$data['studentId'] = $studentId;
		$data["student_terminate_reasons"] = $this->parent_model->getStudentTerminateReasons();
		$data["student_information"]= $this->parent_model->getLoggedInStudentInformation($data['studentId']);
		// Get current and next academic year ids
		$data['acad_year_id_applied_in']=0;
		$acad_year_id_applied_in = (int)$this->settings->getSetting("academic_year_id");
		if($acad_year_id_applied_in){
			$data['acad_year_id_applied_in']=$acad_year_id_applied_in;
		}
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'parent/Student_exit_flow/tablet_index';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content'] = 'parent/Student_exit_flow/mobile_index';
		} else {
			$data['main_content'] = 'parent/Student_exit_flow/index';
		}
	}
	
	$this->load->view('inc/template', $data);
}

private function sendStudentExitApplicationEmailToStaff($subject, $body, $fromMail, $staffs){
// filter staff ids
$receivers=[];
foreach($staffs as $key => $staffId){
	if(empty($staffId)) continue;
	$receivers[]=$staffId;
}

$email_master_data = array(
	'subject' => $subject,
	'body' => $body,
	'source' => 'Manual Template',
	'sent_by' => $this->authorization->getAvatarId(),
	'recievers' => implode(',', $receivers),
	'from_email' => $fromMail,
	'files' => NULL,
	'acad_year_id' => $this->acad_year->getAcadYearId(),
	'visible' => 1,
	'sender_list' => NULL,
	'sending_status' => 'Send Email'
	);

	$this->load->model('communication/emails_model');
	$email_master_id = $this->emails_model->saveEmail($email_master_data);

	// fetch all the staff mails ids whom the email need to send
	$staffEmails=$this->Student_exit_flow_model->getStaffMailsById($receivers);
	$receiversMailIds=[];

	$sent_data = [];
	if (!empty($email_master_id)) {
	foreach ($receivers as $staffId) {
		$obj = new stdClass();
		$obj->email_master_id = $email_master_id;
		$obj->id = $staffId;
		$obj->email = array_key_exists($staffId,$staffEmails) ? $staffEmails[$staffId] : Null;
		$obj->avatar_type = 4;
		$obj->status = array_key_exists($staffId, $staffEmails) ? 'Awaited' : 'No Email';
		$sent_data[] = $obj;
	}
	}

	$this->emails_model->save_sending_data($sent_data, $email_master_id);
	$this->load->helper('email_helper');
	return sendEmail($body, $subject, $email_master_id, $receiversMailIds, $fromMail);
  }

public function check_student_exit(){
	$student_id = $_POST['student_id'];
    $result = $this->parent_model->check_student_exit($student_id);
    echo json_encode($result);
}

public function save_std_exit(){
	// check if any pending tc application pending
	$is_any_un_rejected_tc_exists=$this->parent_model->is_any_un_rejected_tc_exists($_POST);
	if($is_any_un_rejected_tc_exists==1){
		$this->session->set_flashdata('flashSuccess', 'Your TC request already exists');
		redirect('parent_controller/student_exit_flow');
		return;
	}

	$result = $this->parent_model->save_student_exit_flow();

	if ($result["applied"]) {
		$student_id = $_POST["student_id"];
		$student_info = $this->Student_exit_flow_model->get_exit_student_detail($student_id);

		if (!empty($student_info)) {
			$student_name = $student_info->student_name;
			$class_section = $student_info->class_section;
			$message = "New Student '" . $student_name . "' of class '" . $class_section . "' is added for exit.";
			;
		} else {
			$message = "New Student is added for exit.";
		}

		$admins = $this->role->getStaffListByPrivilege('STUDENT_EXIT_FLOW_STAFF', 'ADMIN');
		
		$member_ids = $this->Student_exit_flow_model->get_save_student_exit_flow($result["applied"]);

		$member_ids = array_merge($admins, $member_ids);

		if (!empty($member_ids)) {
			$this->load->helper('texting_helper');
			$input_array = array(
				'mode' => 'notification',
				'title' => 'Student Exit Flow Approval Pending',
				'source' => 'Student Exit Flow',
				'is_unicode' => '0',
				'visible' => 1,
				'staff_ids' => $member_ids,
				'message' => $message
			);
			$response = sendText($input_array);

			// send email-notification
			$fromMail = $this->settings->getSetting("tc_request_notification_from_email");
			if(strlen($fromMail)) {
				$subject = "Received TC application request";
				$body = $message;
				$receivers = $member_ids;
				// SENDING EMAILS TO CONCERNED STAFF MEMBERS AND ADMIN
				$this->sendStudentExitApplicationEmailToStaff($subject, $body, $fromMail, $receivers);
			}
		}
		$this->session->set_flashdata('flashSuccess', 'TC Applied Successfully');
	} else if($result["reApplied"]) {
		$this->session->set_flashdata('flashSuccess', 'TC Re-Applied Successfully');
	} else {
		$this->session->set_flashdata('flashError', 'Something Went Wrong');
	}
    redirect('parent_controller/student_exit_flow');
  }

  public function student_status(){
	$student_id = $_POST['student_id'];
    $result = $this->parent_model->student_status($student_id);
    echo json_encode($result);
}

public function download_consent_form($bp_id){
	$url = $this->parent_model->get_template_form_path($bp_id);
    // $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download('T & C Form.pdf', $data, TRUE);
}



public function download_id_card_as_pdf() {
		try {
			$file_url = $this->input->post('file_url');
			$file_name = $this->input->post('file_name');

			if (empty($file_url)) {
				$this->session->set_flashdata('error', 'File URL is required');
				redirect($this->agent->referrer());
				return;
			}

			if (empty($file_name)) {
				// Extract file extension from URL if possible
				$url_parts = parse_url($file_url);
				$path_parts = pathinfo($url_parts['path']);
				$extension = isset($path_parts['extension']) ? $path_parts['extension'] : 'jpg';
				$file_name = 'IDCard.' . $extension;
			}

			// Initialize cURL for better error handling and header support
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $file_url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 30);
			curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; ID Card Downloader)');

			$data = curl_exec($ch);
			$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			$curl_error = curl_error($ch);
			curl_close($ch);

			if ($data === FALSE || !empty($curl_error)) {
				log_message('error', 'cURL error downloading ID card: ' . $curl_error);
				$this->session->set_flashdata('error', 'Failed to download file from the provided URL');
				redirect($this->agent->referrer());
				return;
			}

			if ($http_code !== 200) {
				log_message('error', 'HTTP error downloading ID card: ' . $http_code);
				$this->session->set_flashdata('error', 'Failed to download file (HTTP ' . $http_code . ')');
				redirect($this->agent->referrer());
				return;
			}

			// Load download helper and force download
			$this->load->helper('download');
			force_download($file_name, $data, TRUE);

		} catch (Exception $e) {
			log_message('error', 'ID Card download error: ' . $e->getMessage());
			$this->session->set_flashdata('error', 'An error occurred while downloading the file');
			redirect($this->agent->referrer());
		}
	}

public function display_fee_blueprints_jodo(){
	$data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
	$data['acad_year_selection'] = $this->parent_model->get_fee_total_acad_year($data['student_id']);
	$data['allacadyears'] = $this->acad_year->getAllYearData();
	$data['currentAcadyearId'] = $this->acad_year->getAcadYearId();
	$data['main_content'] = 'parent/feesv3/index';
	$this->load->view('inc/template', $data);
}

public function pay_fees_jodo($acad_year_id){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	$studentData = $this->fees_collection_model->get_student_data_for_jodoby_student_id($studentId, $acad_year_id);
	if(!empty($studentData)){
		$this->payment_jodo->init_fee_payment_to_school_jodo($studentData, $acad_year_id);
	}else{
		$this->session->set_flashdata('flashError', 'Fees not published yet. Please contact schoool');
		redirect('parent_controller/display_fee_blueprints_jodo/');
	}
}

public function check_consent_forms_submitted(){
	$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
	echo $this->parent_model->check_consent_forms_submitted($studentId);
}

public function get_student_details_mandatory_fields_jodo(){
	$studentId = $_POST['studentId'];
	$acadyearId = $_POST['acadyearId'];
	$result = $this->parent_model->get_student_details_mandatory_fields_jodo($studentId, $acadyearId);
	echo json_encode($result);
}

	public function get_dateWise_tasks() {
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['task'] = $this->parent_model->get_dateWise_tasks($studentId);
		echo json_encode($data);
	}

	public function inventory(){
		$site_url = site_url();
        $data['inventory_master'] = array(
          [
            'title' => 'Place New Order',
            'sub_title' => 'Place New Order',
            'icon' => 'svg_icons/add.svg',
            'url' => $site_url.'parent/inventory',
            'permission' => $this->settings->isParentModuleEnabled('STUDENT_INVENTORY_RESERVE_ITEMS')
          ],
          [
            'title' => 'Reserved Orders',
            'sub_title' => 'Reserved Orders',
            'icon' => 'svg_icons/view.svg',
            'url' => $site_url.'parent/inventory/reserved_orders_view',
            'permission' => $this->settings->isParentModuleEnabled('STUDENT_INVENTORY_RESERVED_ITEMS')
		  ],
		  [
            'title' => 'Delivered Orders',
            'sub_title' => 'Delivered Orders',
            'icon' => 'svg_icons/dailytransaction.svg',
            'url' => $site_url.'parent_controller/student_inventory',
            'permission' => $this->settings->isParentModuleEnabled('STUDENT_INVENTORY')
			]
        );
        $data['inventory_master'] = checkTilePermissions($data['inventory_master']);

		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();

		$is_student_partially_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$deactivated_modules = json_decode($this->settings->getSetting('deactivation_modules'));
		
		$is_deactivated = 0;
		if(!empty($deactivated_modules)){
			if($is_student_partially_deactivated && in_array('e-Kart', $deactivated_modules)) {
				$is_deactivated = 1;
			} else {
				$is_deactivated = 0;
			}	
		}
		if($is_deactivated==1){
			$data["module_name"]="e-Kart";
			$data['main_content'] = 'parent/temporary_deactive_page.php';
		}else{
			$data['main_content'] = 'parent/inventory/index';
		}
		$this->load->view('inc/template', $data);
	}


	public function idcard_parent_dashboard(){
		$site_url = site_url();
        $data['idcard_parent_master'] = array(
          [
            'title' => 'Student ID Card Approval',
            'sub_title' => 'Student Id Card Approval',
            'icon' => 'svg_icons/add.svg',
            'url' => $site_url.'parent_controller/parentApproveStudentWise/student',
            'permission' => $this->settings->isParentModuleEnabled('ID_CARD_APPROVAL_STUDENT')
          ]
		  ,
          [
            'title' => 'Parent ID Card Approval',
            'sub_title' => 'Parent Id Card Approval',
            'icon' => 'svg_icons/view.svg',
            'url' => $site_url.'parent_controller/parentApproveStudentWise/parent',
            'permission' => $this->settings->isParentModuleEnabled('ID_CARD_APPROVAL_PARENT')
		  ]
        );
        $data['idcard_parent_master'] = checkTilePermissions($data['idcard_parent_master']);

		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();

		$is_student_partially_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		// $deactivated_modules = true;
		$deactivated_modules = json_decode($this->settings->getSetting('deactivation_modules'));
		
		$is_deactivated = 0;
		if(!empty($deactivated_modules)){
			if($is_student_partially_deactivated && in_array('ID Card', $deactivated_modules)) {
				$is_deactivated = 1;
			} else {
				$is_deactivated = 0;
			}	
		}
		if($is_deactivated==1){
			$data["module_name"]="ID Card";
			$data['main_content'] = 'parent/temporary_deactive_page.php';
		}else{
			$data['main_content'] = 'idcard/idcard_parent_dashboard_view';
		}

		
		$this->load->view('inc/template', $data);
	}

	public function student_inventory(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['inventory_data'] =$this->parent_model->getStudentInventoryReport($studentId);
		$data['main_content'] = 'parent/inventory/parent_inventory_report';
		$this->load->view('inc/template', $data);
	}

	public function get_items_by_master_id(){
		$master_id = $this->input->post('master_id');
		$result = $this->parent_model->get_items_by_master_id($master_id);
		echo json_encode($result);
	}

	public function display_fee_blueprints_multiple_selection(){
		$data['main_content'] = 'parent/fees_mulitple_blueprints/index';
		$this->load->view('inc/template', $data);
	}

	public function pay_mulitple_fees(){
		$stake_holder_id = $this->authorization->getAvatarStakeHolderId();
		$data['std_adm_id'] = $this->parent_model->get_student_id_by_user_id($stake_holder_id);
		$data['main_content'] = 'parent/fees_mulitple_blueprints/pay_fees';
		$this->load->view('inc/template', $data);
	}

	public function get_student_fees_installment_wise_data(){
		$stdAdmId = $_POST['stdAdmId'];
		$result  = $this->parent_model->get_all_published_fee_data($stdAdmId);
		// echo "<pre>"; print_r($result);die();
		echo json_encode($result);
	}

	public function multiple_fees_pay(){
		$input = $this->input->post();
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$transPayAmount = 0;
		foreach ($input['pay_amount'] as $key => $value1) {
			foreach ($value1 as $key2 => $value2) {
				foreach ($value2 as $key3 => $amount) {
					$transPayAmount += $amount;
				}
			}
		}
		// $totalFine = 0;
		// foreach ($input['fine_amount'] as $installments) {
		// 	foreach ($installments as $components) {
		// 		$totalFine += array_sum(array_filter($components, 'is_numeric'));
		// 	}
		// }

		$totalFine = 0;
		foreach ($input['fine_amount'] as $fee_student_schedule_id => $installments) {
			foreach ($installments as $installment_id => $components) {
				foreach ($components as $component_id => $amount) {
					if (is_numeric($amount)) {
						$totalFine += floatval($amount);
					}
				}
			}
		}

		$fees_concession = [];
		$fees_concession_discount = [];
		$totalConcession = 0;
		foreach ($input['concession_component'] as $student_id => $installments) {
			foreach ($installments as $installment_id => $components) {
				$first_component_id = null;
				foreach ($components as $key => $value) {
					$first_component_id = $key;
					break;
				}
				foreach ($components as $component_id => $component_amount) {
					$fees_concession[$student_id][$installment_id][$component_id] = $component_amount;
					$fees_concession_discount[$student_id][$installment_id][$component_id] = 0;
					if (isset($input['concession_discount'][$student_id][$installment_id]) && $component_id === $first_component_id) {
						$fees_concession[$student_id][$installment_id][$component_id] += $input['concession_discount'][$student_id][$installment_id];
						$fees_concession_discount[$student_id][$installment_id][$component_id] += $input['concession_discount'][$student_id][$installment_id];
					}

					$totalConcession += $fees_concession_discount[$student_id][$installment_id][$component_id];
				}
			}
		}
		$discountAmount = array_sum($input['discount_amount']);

		$transaction_ids = $this->parent_model->insert_multiple_fee_transactions($input['pay_amount'], $fees_concession, $input['fine_amount'],$input['discount_amount'], $studentId, $fees_concession_discount);
		$this->parent_model->insert_multiple_installment_transactions_components($input['component_details'], $fees_concession, $input['fine_amount'], $transaction_ids, $fees_concession_discount);
		$this->parent_model->insert_multiple_installment_transactions_paments($transaction_ids);

		// concession /discount
		if(!empty($fees_concession_discount)){
			foreach($fees_concession_discount as $scheduleId => $installments) {
				$this->parent_model->insert_discount_concession_details_temp($transaction_ids[$scheduleId], $scheduleId, $installments);
			}
		}

		$totalFineAmount = $totalFine;
		$totalDiscount = 0;
		$transPayAmount = $transPayAmount + $totalFineAmount - $totalConcession - $discountAmount;
		$component_details_with_discount_fine = [];
		foreach ($input['component_details'] as $fee_student_schedule_id => $installments) {
			foreach ($installments as $installment_key => $components) {
				foreach ($components as $component_key => $amount_paid) {
					list($installment_id, $component_id) = explode('_', $installment_key);
					list($installment_component_id, $sub_component_id) = explode('_', $component_key);

					$fine = isset($input['fine_amount'][$fee_student_schedule_id][$installment_id][$installment_component_id]) 
						? floatval($input['fine_amount'][$fee_student_schedule_id][$installment_id][$installment_component_id]) 
						: 0;
					$concession = isset($fees_concession_discount[$fee_student_schedule_id][$installment_id][$installment_component_id]) 
						? floatval($fees_concession_discount[$fee_student_schedule_id][$installment_id][$installment_component_id]) 
						: 0;
					$final_amount = $amount_paid + $fine - $concession;
					$component_details_with_discount_fine[$fee_student_schedule_id][$installment_key][$component_key] = $final_amount;
				}
			}
		}

		$payment_split = $this->parent_model->get_multiple_split_amount($input['bluerint_id'], $component_details_with_discount_fine);
		foreach ($payment_split->vendors as $key => &$value) {
			if ($key == 0) {
				$value->split_amount_fixed = $value->split_amount_fixed - $discountAmount;
			}
		}
		$transIds= [];
		foreach ($transaction_ids as $key => $val) {
			array_push($transIds,$val);
		}
		$this->payment->init_payment_to_school($transPayAmount, 'PARENT_FEE', json_encode($transIds), 'parent_controller/fee_multiple_trans_done', 1, json_encode($payment_split), 'REDIRECT');
	}

	public function fee_multiple_trans_done(){
		$this->__handle_fee_multiple_op_response($_POST);
	}

	private function __handle_fee_multiple_op_response($response){
		if ($response['transaction_status'] === 'SUCCESS') {
			$sourceIds = json_decode($response['source_id'], true);
			foreach ($sourceIds as $key => $source_id) {
				$is_receipt_generated = $this->fees_collection_model->is_receipt_generated($source_id);
				if ($is_receipt_generated) {
					continue;
				}
				$this->fees_collection_model->update_concession_table_transaction_wise($source_id);
				
				$this->fees_collection_model->update_trans_student_all_table($source_id);
				
			}
			$source_id_encoded = urlencode(json_encode($response['source_id']));

			redirect('parent_controller/fee_payment_success_receipt_construct/' . $source_id_encoded . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);

		} else {
			$sourceIds = json_decode($response['source_id']);
			//Online payment failed
			foreach ($sourceIds as $key => $source_id) {
				$this->fees_collection_model->update_transcation_status($source_id, 'FAILED');
			}
			$source_id_encoded = urlencode(json_encode($response['source_id']));
			$transaction_id = $response['transaction_id'];
			if(empty($response['transaction_id'])){
				$transaction_id = 0;
			}
			redirect('parent_controller/failed_payment_for_mulitple_fee/' . $source_id_encoded . '/' . $transaction_id . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
		}
	}

	public function fee_payment_success_receipt_construct($source_id_encoded,$transaction_id, $transaction_date,$transaction_time){
		$sourceIds = json_decode(trim(urldecode($source_id_encoded), '"'), true);
		
		foreach ($sourceIds as $key => $fTrans) {
			$this->fees_collection_model->create_pdf_template_for_fee_receipts($fTrans, $transaction_id, $transaction_date, $transaction_time);
			// $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);	
			// $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
		}
		$getTotalAmountPaid = $this->parent_model->get_fee_transaction_total_amount_paid($sourceIds);
		$fee_payment_sms = $this->settings->getSetting('fee_payment_sms');
		if (!empty($fee_payment_sms)) {
			if ($fee_payment_sms->sms_enabled == TRUE) {
				$this->__sms_fees_payment($fee_payment_sms, $transaction_id, $getTotalAmountPaid);
			}
		}
		redirect('parent_controller/fee_multifee_reciept_view/' . $source_id_encoded . '/' . $transaction_id . '/' . $transaction_date . '/' . $transaction_time);
	}

	public function failed_payment_for_mulitple_fee($source_id_encoded,$transaction_id, $transaction_date,$transaction_time){
		$sourceIds = json_decode(trim(urldecode($source_id_encoded), '"'), true);

		$data['transaction_id'] = $transaction_id;
		$data['transaction_date'] = $transaction_date;
		$data['transaction_time'] = $transaction_time;
		$data['fee_trans'] = $this->parent_model->get_fees_amount_payment_after($sourceIds);
		$data['main_content'] = 'parent/fees_mulitple_blueprints/failed_details';
		$this->load->view('inc/template', $data);
	}

	public function fee_multifee_reciept_view($source_id_encoded,$transaction_id, $transaction_date,$transaction_time){
		$sourceIds = json_decode(trim(urldecode($source_id_encoded), '"'), true);
		$data['fee_trans'] = $this->parent_model->get_fees_amount_payment_after($sourceIds);
		$data['transaction_id'] = $transaction_id;
		$data['transaction_date'] = $transaction_date;
		$data['transaction_time'] = $transaction_time;
		$data['main_content'] = 'parent/fees_mulitple_blueprints/payment_details';
		$this->load->view('inc/template', $data);
	}

	public function get_referal_link() {
		$admission_number = $this->parent_model->get_admission_number($_POST['student_id']);
		if (empty($admission_number)) {
			return json_encode(0);
		}
		
		$key = "1a2b3c4d5e6f7g8h9i0j"; 
		$method = "AES-128-CTR"; 
		$ivLength = openssl_cipher_iv_length($method);
		$iv = substr(hash('sha256', $key), 0, $ivLength); 
	
		// Encrypt the admission number
		$encrypted = openssl_encrypt($admission_number, $method, $key, 0, $iv);
	
		// Base64 encode the encrypted value
		$encrypted_val = base64_encode($encrypted);
	
		// Construct the full URL
		$full_url = base_url() . "enquiries/referral/" . urlencode($encrypted_val);
		
		echo json_encode($full_url);
	}
	
	public function submit_student_transport(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$result = $this->parent_model->submit_student_transport($studentId);
		if($_POST['submit_type'] == 're_submit'){
			$this->parent_model->store_transport_edit_history($studentId);
		}
		$get_email_template_data = $this->parent_model->get_transport_email_data($studentId);
		if(!empty($get_email_template_data)){
			$send_email = $this->_transportation_request_email_to_staff($get_email_template_data);
		}
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Inserted.');
		}else{
			$this->session->set_flashdata('flashError', 'Failed to update .');
		}
		redirect('parent_controller/transport_request');
	}

	public function get_transportation_details(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$result = $this->parent_model->get_assigned_transport($studentId,$_POST['acad_year_id']);
		if(!empty($result)){
				$fee_status = $this->parent_model->check_non_refund_transport_fees_status($studentId,$_POST['acad_year_id']);
				$result->fee = $fee_status;
		}
		$pickup_mode = $this->settings->getSetting('transport_mode');
		if(!empty($result) && !empty($result->pickup_mode) && !empty($pickup_mode)){
			foreach($pickup_mode as $key => $val){
				if($result->pickup_mode == $val->value){
				  $result->pickup_mode = $val->name;
				}
			}
		}
		echo json_encode($result);
	}

	public function check_fees_transportation_details(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$fee_status = $this->parent_model->check_non_refund_transport_fees_status($studentId,$_POST['acad_year_id']);
		echo json_encode($fee_status);
	}

	public function assing_transport_fees(){
		$loggedInstudentId = $this->input->post('loggedInstudentId');
		$acad_year_id = $this->input->post('acad_year_id');
		$assign_fees = $this->parent_model->assinged_fees_for_student($loggedInstudentId,$acad_year_id);
		$this->parent_model->submit_parent_details();
		$cohort_student_id = 0;
		if(!empty($assign_fees)){
			foreach ($assign_fees as $bpId => $value) {
			  $res = $this->fees_student_model->insert_cohort_details($bpId, $value['cohort_id'], 'STANDARD', $value['blueprint_installment_type_id'], $value['comp_amount'], $value['concession_amount'], $value['student_id'], '', $value['fine_amount']);
			  if(!empty($res)){
				$cohort_student_id = $res['cohort_student_id'];
				$this->fees_student_model->publish_assigned_fee((array) $cohort_student_id);
			  }else{
				$cohort_student_id = $this->fees_student_model->check_cohort_student_id($bpId,$value['student_id']);
			  }
			}
		}
		echo json_encode(array('cohort_student_id'=>$cohort_student_id, 'student_id'=>$loggedInstudentId));
	}

	public function transport_fee_pay($cohort_student_fee_id, $student_id){
		$feeData = $this->parent_model->get_bp_id_by_cohort_student_fee_id($cohort_student_fee_id);
		if(empty($feeData)){
			return false;
		}
		$input = array();
		$input['cohort_student_id'] = $cohort_student_fee_id;
		$input['student_id'] = $student_id;	
		$input['payment_type'] = '10_0';	
		$input['payment_type_choose'] = '';	
		$input['transaction_mode'] = 'ONLINE';	
		$input['receipt_date'] = date('d-m-Y');	
		$input['total_amount'] = $feeData->total_fee;
		$bpSchudeAmount = $this->_consturct_fee_blueprint_wise_data($feeData->blueprint_id, $feeData->total_fee, $student_id);
		$mergeInput = array_merge($input, $bpSchudeAmount);
		$this->_pay_transportation_fee($mergeInput);
	}

	private function _pay_transportation_fee($input){
		$blue_print = $this->fees_collection_model->ge_blueprint_by_id($input['cohort_student_id']);

		$this->db->trans_begin();
		$schoolName = $this->settings->getSetting('school_short_name'); // temp testing  after remove this code
		if($schoolName =='skalvi'){
			$payment_split = $this->fees_collection_model->get_split_strategy_amount($blue_print->id, $input['split_amount']);
		}else{
			$payment_split = $this->fees_collection_model->get_split_amount($blue_print->id, $input['split_amount']);
		}
		$fTrans = $this->fees_collection_model->insert_fee_transcation($input);

		if (!$fTrans) {
			$this->session->set_flashdata('flashError', 'Transcation failed to insert');
			$this->db->trans_rollback();
			redirect('parent_controller/transport_request');
		} else {
			$this->db->trans_commit();
			$totalFineAmount = 0;
			$totalDiscount = 0;
			if (isset($input['total_fine_amount'])) {
				$totalFineAmount = $input['total_fine_amount'];
			}
			if (isset($input['discount_amount'])) {
				$totalDiscount = $input['discount_amount'];
			}

			foreach ($payment_split->vendors as $key => &$value) {
				if ($key == 0) {
					$value->split_amount_fixed = $value->split_amount_fixed + $totalFineAmount - $totalDiscount;
				}
			}
			
			$transPayAmount = $input['total_amount'] + $totalFineAmount - $totalDiscount;

			$payment_gateway = $this->settings->getSetting('payment_gateway');
			switch ($payment_gateway) {
				case 'payu':
					$this->payment_payu->init_fee_payment_to_school($transPayAmount, $fTrans);
					break;
				case 'jodo':
					$payment_split_jodo = $this->fees_collection_model->get_split_amount_jodo($blue_print->id, $input['split_amount']);
					$jodo_split = [];
					foreach ($payment_split_jodo as $key => $val) {
						if($val->amount != 0){
							array_push($jodo_split, $val);
						}
					}
					if($input['payment_type_choose'] =='Flex'){
						$studentData = $this->fees_collection_model->get_student_data_for_jodoby_student_id($input['student_id']);
						$this->payment_jodo->init_fee_payment_to_school_flex($transPayAmount, $fTrans, $jodo_split, $input['payment_type_choose'], $studentData);
					}
					$this->payment_jodo->init_fee_payment_to_school($transPayAmount, $fTrans, $jodo_split);
					break;
				default:
					$this->payment->init_payment_to_school($transPayAmount, 'PARENT_FEE', $fTrans, 'parent_controller/fee_trans_done_transport', $blue_print->is_split, json_encode($payment_split), 'REDIRECT');
					break;
			}
		}
	}

	public function _consturct_fee_blueprint_wise_data($bpId, $bp_amount, $student_id){
		$result = $this->db->select('fsic.id as fee_student_installments_components_id, fsic.blueprint_component_id, ifnull(fsic.component_amount,0) as component_amount, ifnull(component_amount_paid,0) as component_amount_paid, ifnull(concession_amount,0) as concession_amount, ifnull(concession_amount_paid,0) as concession_amount_paid, fsi.id as fee_student_installments_id, fsi.feev2_installments_id, ifnull(fsi.installment_amount,0) as installment_amount, ifnull(fsi.installment_amount_paid,0) as installment_amount_paid, fsi.total_concession_amount, ifnull(fsi.total_concession_amount_paid,0) as total_concession_amount_paid, fss.id as fee_student_schedule_id, (ifnull(fsic.component_amount,0) - ifnull(fsic.component_amount_paid,0) -  ifnull(fsic.concession_amount,0) - ifnull(fsic.concession_amount_paid,0)) as balance_amount, fcs.id as cohort_student_id')
			->from('feev2_cohort_student fcs')
			->where('fcs.student_id',$student_id)
			->where('fcs.blueprint_id',$bpId)
			->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
			->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
			->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
			->get()->result();

			$components_split = array();
			$input = array();
			$tAllocAmount = $bp_amount;
			foreach ($result as $key => $val) {    
			if($tAllocAmount >= $val->balance_amount){
				$input['pay_amount'][$val->feev2_installments_id][$val->blueprint_component_id] =  $val->balance_amount;
				$tAllocAmount = $tAllocAmount - $val->balance_amount;
			}else{
				$input['pay_amount'][$val->feev2_installments_id][$val->blueprint_component_id] =  $tAllocAmount;
				$tAllocAmount = $tAllocAmount - $val->balance_amount;
				if ($tAllocAmount < 0) $tAllocAmount = 0; 
			} 
			$input['conc_amount'][$val->feev2_installments_id][$val->blueprint_component_id] =  $val->concession_amount;
			$input['comp_id'][] = $val->blueprint_component_id;
			$input['fsicompId'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->fee_student_installments_components_id;
			$input['fsInsId'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->fee_student_installments_id;
			$input['split_amount'][$val->blueprint_component_id] = $val->balance_amount;
			$input['cohort_student_id'] = $val->cohort_student_id;
			$input['fee_student_schedule_id'] = $val->fee_student_schedule_id;
		}
		return $input;
	}
	

	private function _transportation_request_email_to_staff($email_data){
		$this->load->helper('email_helper');
		$emailIds = explode(',', $email_data['members_email']);
		$emailIds = array_filter($emailIds);
		
		if(empty($emailIds)){
			return 0;
		}
		$email_data['content'] = str_replace('%%student_name%%',$email_data['to_email']->student_name,$email_data['content']);
		$email_data['content'] = str_replace('%%class_name%%',$email_data['to_email']->class_section_name,$email_data['content']);
		$email_data['content'] = str_replace('%%enrollment_number%%',$email_data['to_email']->enrollment_number,$email_data['content']);
		$email_data['content'] = str_replace('%%admission_number%%',$email_data['to_email']->admission_no,$email_data['content']);

		$acad_year_id = $this->settings->getSetting('academic_year_id');

		
		$emails_data = [];
		foreach($emailIds as $key => $val){
			$email_master_data = array(
				'subject' => $email_data['email_subject'],
				'body' => $email_data['content'],
				'source' => 'Transportation Requested',
				'sent_by' => '',
				'recievers' => "Staff",
				'from_email' => $email_data['registered_email'],
				'files' => NULL,
				'acad_year_id' => $acad_year_id,
				'visible' => 1,
				'sender_list' => $val,
				'sending_status' => 'Completed'
			);
			$this->load->model('communication/emails_model');
			$email_master_id = $this->emails_model->saveEmail($email_master_data);

			$email_obj = new stdClass();
			$email_obj->stakeholder_id = '';
			$email_obj->avatar_type = 2;
			$email_obj->email = $val;
			$email_obj->email_master_id = $email_master_id;
			$email_obj->status = ($val)?'Awaited':'No Email';
			$emails_data[] = $email_obj;
		}

		$this->parent_model->save_sending_email_data($emails_data);
		return sendEmail($email_data['content'], $email_data['email_subject'], 0, $emailIds, $email_data['registered_email'], '');

	}

	public function parentApproveStudentWise(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_id'] = $studentId;
		$data['student_details'] = $this->Idcards_model->getStudentDetails($studentId);
		
		$data['parent_idcards']=$this->Idcards_model->getParentIdCards($studentId);
		// echo "<pre>"; print_r($data['parent_idcards']); die();
		
        if ($this->mobile_detect->isMobile() || $this->mobile_detect->isTablet()) {
			$data['main_content'] = 'idcard/parent_mobileView';
		} else {
			$data['main_content'] = 'idcard/parent_desktopView';
		}

		$this->load->view('inc/template', $data);
    }

    public function aprrove_idcards_templates() {
   
        $result = $this->Idcards_model->aprrove_idcards_templates();

        // Return JSON response based on the result
        if ($result === true) {
            echo json_encode([
                'success' => true,
                'message' => 'ID card approved successfully'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to approve ID card'
            ]);
        }
    }
	public function getImageAsBase64() {
        header('Content-Type: application/json');

        $image_url = $this->input->post('image_url');

        if (!$image_url) {
            echo json_encode(['success' => false, 'message' => 'No image URL provided']);
            return;
        }

        try {
            // Initialize cURL session
            $ch = curl_init();

            // Set cURL options
            curl_setopt($ch, CURLOPT_URL, $image_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

            // Execute cURL session
            $image_data = curl_exec($ch);

            // Check for cURL errors
            if (curl_errno($ch)) {
                throw new Exception(curl_error($ch));
            }

            // Get content type
            $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

            // Close cURL session
            curl_close($ch);

            // Convert to base64
            $base64 = 'data:' . $content_type . ';base64,' . base64_encode($image_data);

            echo json_encode(['success' => true, 'base64' => $base64]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error fetching image: ' . $e->getMessage()
            ]);
        }
    }
	public function getIdCardTemplate() {
        $order_id = $this->input->post('order_id');

        // Get order details to find the template ID
        $order_details = $this->Idcards_model->getOrderDetails($order_id);

        if (!$order_details || !$order_details->idcard_template_id) {
            echo json_encode(['error' => 'Template not found']);
            return;
        }

        // Get the template data
        $template = $this->Idcards_model->get_template($order_details->idcard_template_id);

        if (!$template) {
            echo json_encode(['error' => 'Template not found']);
            return;
        }

        // Return the template data
        echo json_encode([
            'success' => true,
            'template' => $template
        ]);
    }

	public function getProfileCompletionStatus(){
		 $studentId = $this->parent_model->getStudentIdOfLoggedInParent();

		  $status = $this->parent_model->getProfileCompletionStatus($studentId);
		  echo json_encode($status);


	}

	public function attendance_day_v2(){
		
		 $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		
		
		 $getOverallDetails = $this->attendance_day_v2_model->getSummaryDetailsStdWise($studentId);

		//  echo "<pre>"; print_r($getOverallDetails);die();
		
		$data['getOverallDetails'] = $getOverallDetails;
		$data['studentId'] = $studentId;
		// echo "<pre>"; print_r($data);die();
		
		if ($this->mobile_detect->isMobile()) {
			$data['main_content'] = 'parent/attendace_day_v2/attendance_day_v2_mobileView';
		} else {
			$data['main_content'] = 'parent/attendace_day_v2/attendance_day_v2_desktopView';
		}
		
		$this->load->view('inc/template', $data);
	}

	public function get_attendance_events() {
		$start = $this->input->post('start');
		$end = $this->input->post('end');
		$student_id = $this->input->post('student_id');
	
		$attendance_data = $this->attendance_day_v2_model->getAttendanceDataByDateRange($start, $end, $student_id);
	
		// Output as JSON
		echo json_encode($attendance_data);
	}
	public function generate_qr_code_for_idcards(){
		$this->load->library('ciqrcode');
		$data = $this->input->get('qrdata');
		$size = $this->input->get('qr_size');
		// Define QR code parameters
		$params = array(
			'data' => $data,
			'level' => 'H', // Error correction level: L, M, Q, H
			'size' => $size,    // Size of the QR code
			'savename' => null // No file save, output to buffer
		);
		
		ob_start();
		$this->ciqrcode->generate($params, false);
		$qrCodeBinary = ob_get_clean();

		// Encode the QR code as base64
		$base64 = base64_encode($qrCodeBinary);
		echo 'data:image/png;base64,' . $base64;
	}

	public function get_png_idcards(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();

		$png_images = $this->parent_model->get_png_idcards($studentId);
		echo json_encode($png_images);
	}
	

}
?>
